package com.anytech.anytxn.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.enums.StatusEnum;
import com.anytech.anytxn.business.base.transaction.domain.bo.TransRecordResultBO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.LiabilityEnum;
import com.anytech.anytxn.common.core.enums.RelationshipIndicatorEnum;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.installment.base.domain.bo.AdjustInstallTermBo;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.constants.InstallmentRuleConstant;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallFeeCalculationService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import com.anytech.anytxn.installment.service.interest.Rule78Interest;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.AcctProductInfoDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IAcctProductMainInfoService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamtrSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamtr;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.transaction.base.enums.MessageIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.ReverseFeeIndicatorEnum;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2019-05-27 14:19
 **/
@Service
public class InstallOrderServiceImpl implements IInstallOrderService {
    private static final Logger logger = LoggerFactory.getLogger(InstallOrderServiceImpl.class);

    @Autowired
    private IInstallFeeCalculationService installFeeCalculationService;
    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private IInstallPlanService installPlanService;
    @Autowired
    private InstallOrderMapper installOrderMapper;

    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private TPmsGlamtrSelfMapper tPmsGlamtrSelfMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private PostedTransactionMapper postedTransactionMapper;

    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IStatementProcessService statementProcessService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Autowired
    private CardBasicInfoMapper cardBasicInfoMapper;
    @Autowired
    private InstallmentThreadLocalHolder installmentThreadLocalHolder;
    @Autowired
    private IAcctProductMainInfoService acctProductMainInfoService;




    /**
     * 创建分期订单
     *
     * @param installParameter 订单参数
     * @return int
     **/
    @BatchSharedAnnotation
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String add(InstallParameterDTO installParameter) {
        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
        logger.info("Creating installment order: installOrderDTO={}", installOrderDTO.getOrderId());
        //必输项检查
        mustPassCheck(installOrderDTO);
        //检查订单是否重复
        int repeat = installOrderSelfMapper.isExistsByCondition(installOrderDTO.getCardNumber()
                , installOrderDTO.getTransactionDate()
                , installOrderDTO.getAuthorizationCode()
                , installOrderDTO.getInstallmentAmount());


        if (repeat > 0) {
            logger.error("Installment order already exists: accountManagementId={}, productCode={}, transactionDate={}, authCode={}, installAmount={}",
                    installOrderDTO.getAccountManagementId(),
                    installParameter.getInstallProInfo().getProductCode(),
                    installOrderDTO.getTransactionDate(),
                    installOrderDTO.getAuthorizationCode(),
                    installOrderDTO.getInstallmentAmount());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_ALREADY_EXIST_FAULT);
        }

        LocalDate firstPostDate = calculateFirstPostDate(installParameter.getInstallOrder(),installParameter.getInstallProInfo());
        installOrderDTO.setFirstPostDate(firstPostDate);
        installOrderDTO.setAbsStatus("N");
        installParameter.setInstallOrder(installOrderDTO);

        /*
         * DO:在调用分期手续费计算组件之前，增加分期手续费差异化定价规则的处理 2020/07/07 by lvxile
         */
        Map<String, Object> ruleResult = installmentDiffPriceRuleCheck(installOrderDTO);
        //匹配定价规则成功
        if (ruleResult != null && !ruleResult.isEmpty()) {
            //分期费用代码的所有参数id
            String feeCodeId = String.valueOf(ruleResult.get(InstallmentRuleConstant.RULE_RESULT_KEY_1));
            logger.info("Card {} creating installment order product [{}] triggered differential pricing rule: feeCodeId={}",
                    installOrderDTO.getCardNumber(), installParameter.getInstallProInfo().getProductCode(), feeCodeId);
            installParameter.getInstallProInfo().setFeeCodeId(feeCodeId);
        } else {
            logger.info("Card {} creating installment order product [{}] did not trigger differential pricing rule",
                    installOrderDTO.getCardNumber(), installParameter.getInstallProInfo().getProductCode());
        }
        /*
            分期手续费差异化定价规则的处理 end
            封装手续费计费组件
        */
        InstallRateCalDTO installRateCal = getInstallRateCal(installParameter);
       //封装订单
        installParameter = packageInstallOrder(installRateCal, installParameter);

        //一次性收取手续费,不在分期订单中再次收取手续费
        feeAmountAccount(installParameter);

        //如果分期本金金额小于最小还款额收取手续费
        minPaymentFeeAmountAccount(installParameter);

        //封装计划
        List<InstallPlan> installPlans = pakagePlan(installParameter);

        //调核心入账
        //为了发生入账拒绝时不创建订单，建调入账提前
        Boolean isSuccess = coreAccount(installParameter);
        if (!isSuccess) {
            return null;
        }


        int i = 0;
        try {
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallOrder(installParameter.getInstallOrder(), false);
            } else {
                InstallOrderDTO installOrder = installParameter.getInstallOrder();
                CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(installOrder.getCardNumber(),installOrder.getOrganizationNumber());


                installOrder.setCustomerId(getCustomerId(cardAuthorizationInfo));
                installOrder.setPartitionKey(getPartitionKey(cardAuthorizationInfo));
                //中行压测中国在分期订单中添加分区键结束
                InstallOrder order = BeanMapping.copy(installParameter.getInstallOrder(),InstallOrder.class);
                i = installOrderMapper.insertSelective(order);
                if (i != 1) {
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to insert new installment order into database", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
        }
        //批量插入计划表
        insertPlanBatch(installPlans);




        logger.info("Installment order creation completed: cardNumber={}, orderId={}",
                installParameter.getInstallOrder().getCardNumber(), installParameter.getInstallOrder().getOrderId());
        return installParameter.getInstallOrder().getOrderId();
    }


    private Long getPartitionKey(CardAuthorizationInfo cardAuthorizationInfo){
        String customerId;
        if (Objects.equals(LiabilityEnum.CORPORATE.getCode(), cardAuthorizationInfo.getLiability())) {
            customerId =  cardAuthorizationInfo.getCorporateCustomerId();
        }else {
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        }

        return PartitionKeyUtils.partitionKey(customerId);
    }


    private String getCustomerId(CardAuthorizationInfo cardAuthorizationInfo){

        if (Objects.equals(LiabilityEnum.CORPORATE.getCode(), cardAuthorizationInfo.getLiability())) {
            return cardAuthorizationInfo.getCorporateCustomerId();
        }

        if (RelationshipIndicatorEnum.MIAN_CARD.getCode().equals(cardAuthorizationInfo.getRelationshipIndicator())) {
            return cardAuthorizationInfo.getPrimaryCustomerId();
        } else {
            return cardAuthorizationInfo.getSupplementaryCustomerId();
        }
    }




    /**
     * 如果分期本金金额小于最小还款额收取手续费
     * 手续费金额 = 本次分期本金
     * @param installParameter 分期参数
     */
    private void minPaymentFeeAmountAccount(InstallParameterDTO installParameter) {

        BigDecimal minPaymentAmount = Optional.ofNullable(installParameter.getInstallProInfo().getMinPaymentAmount()).orElse(BigDecimal.ZERO);

        //分期本金金额 = 分期金额 / 总期数
        BigDecimal installAmount = installParameter.getInstallOrder().getInstallmentAmount();
        Integer installTerm = installParameter.getInstallOrder().getTerm();


        if (installAmount.divide(new BigDecimal(installTerm),6,BigDecimal.ROUND_DOWN).compareTo(minPaymentAmount) < 0 ){
            //收取一次手续费
            BigDecimal feeAmount = installParameter.getInstallOrder().getInstallmentAmount();

            RecordedBO recorded = new RecordedBO();
            //卡号
            recorded.setTxnCardNumber(installParameter.getInstallOrder().getCardNumber());
            //全局业务流水号
            recorded.setTxnGlobalFlowNumber(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
            //入账方式
            recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
            //拒绝重入账标志
            recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
            //冲减交易费用标识
            recorded.setTxnReverseFeeIndicator(ReverseFeeIndicatorEnum.NO.getCode());

            String postingTransactionParmId = installParameter.getInstallProInfo().getPostingTransactionParmId();
            InstallAccountingTransParmResDTO installAccountTran = getInstallAccountTranByOrgNumAndTableId(installParameter.getInstallOrder().getOrganizationNumber(), postingTransactionParmId);

            //交易码
            recorded.setTxnTransactionCode(installAccountTran.getFeeTransactionCode());
            //交易来源
            recorded.setTxnTransactionSource(TransactionSourceEnum.INNER_FEE.getCode());
            //交易描述
            recorded.setTxnTransactionDescription(installParameter.getInstallOrder().getTransactionDesc()
                + "PROCESSING FEE");

            //交易日期
            recorded.setTxnTransactionDate(installParameter.getInstallOrder().getTransactionDate());
            //交易金额
            recorded.setTxnTransactionAmount(feeAmount);
            //交易币种,交易级账户的币种（currency）
            recorded.setTxnTransactionCurrency(installParameter.getInstallOrder().getInstallmentCcy());
            //入账日期
            recorded.setTxnBillingDate(LocalDate.now());
            //入账金额
            recorded.setTxnBillingAmount(feeAmount);
            //入账币种
            recorded.setTxnBillingCurrency(installParameter.getInstallOrder().getInstallmentCcy());
            //清算金额
            recorded.setTxnSettlementAmount(feeAmount);
            //清算币种
            recorded.setTxnSettlementCurrency(installParameter.getInstallOrder().getInstallmentCcy());
            //授权匹配标志,0=未匹配授权
            recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
            //是否恢复授权占用额度标志,N:不需要
            recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
            //单双信息赋值
            recorded.setMessageIndicator(MessageIndicatorEnum.OTHER_INDICATOR.getCode());

            TransRecordResultBO recordResultBO = txnRecordedService.txnRecorded(recorded);
            logger.info("One-time fee collection result: result={}", recordResultBO.getResult());
            if (recordResultBO.getResult() == 1) {
                logger.error("One-time fee collection failed");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.S_TRANS_REJECT);
            }

        }
    }


    /**
     * 创建分期订单
     *
     * @param installParameter 订单参数
     * @return int
     **/
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnInstallException.class)
    public AdjustInstallTermBo adjustInstallOrderTerm(InstallParameterDTO installParameter) {
        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
        //必输项检查
        mustPassCheck(installOrderDTO);
        installOrderDTO.setAbsStatus("N");
        installParameter.setInstallOrder(installOrderDTO);

        /*
         * DO:在调用分期手续费计算组件之前，增加分期手续费差异化定价规则的处理 2020/07/07 by lvxile
         */
        Map<String, Object> ruleResult = installmentDiffPriceRuleCheck(installOrderDTO);
        //匹配定价规则成功
        if (ruleResult != null && !ruleResult.isEmpty()) {
            //分期费用代码的所有参数id
            String feeCodeId = String.valueOf(ruleResult.get(InstallmentRuleConstant.RULE_RESULT_KEY_1));
            logger.info("Card {} creating installment order product [{}] triggered differential pricing rule: feeCodeId={}",
                    installOrderDTO.getCardNumber(), installParameter.getInstallProInfo().getProductCode(), feeCodeId);
            installParameter.getInstallProInfo().setFeeCodeId(feeCodeId);
        } else {
            logger.info("Card {} creating installment order product [{}] did not trigger differential pricing rule",
                    installOrderDTO.getCardNumber(), installParameter.getInstallProInfo().getProductCode());
        }
        /*
        分期手续费差异化定价规则的处理 end
        */

        //封装手续费计费组件
        InstallRateCalDTO installRateCal = getInstallRateCal(installParameter);
        //封装订单
        installParameter = packageInstallOrder(installRateCal, installParameter);
        //封装计划
        List<InstallPlan> installPlans = pakagePlan(installParameter);
        AdjustInstallTermBo adjustInstallTermBo = new AdjustInstallTermBo();
        adjustInstallTermBo.setInstallOrderDto(installParameter.getInstallOrder());
        adjustInstallTermBo.setInstallPlanList(installPlans);
        return adjustInstallTermBo;
    }

    /**
     * 分期试算
     * @param installEntryDTO 分期录入实体
     * @return 分期费用试算
     */
    @Override
    public InstallTrialResDTO trialInstallFee(InstallEntryDTO installEntryDTO){
        InstallParameterDTO installParameter = new InstallParameterDTO();
        String organizationNumber = installEntryDTO.getOrganizationNumber();
        String installProductCode = installEntryDTO.getProductCode();
        InstallProductInfoResDTO installProConf = installProductInfoService.findByIndex(organizationNumber, installProductCode);
        String postingTransactionParmId = installProConf.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO accountTran = installAccountingTransParmService.selectByIndex(organizationNumber, postingTransactionParmId);
        InstallOrderDTO installOrderDTO = new InstallOrderDTO();
        installOrderDTO.setOrganizationNumber(organizationNumber);
        installOrderDTO.setType(installEntryDTO.getInstallType());
        installOrderDTO.setAccountManagementId(installEntryDTO.getAccountManagementId());
        installOrderDTO.setInstallPriceFlag(installEntryDTO.getInstallPriceFlag());
        installOrderDTO.setTotalFeeAmount(installEntryDTO.getInstallTotalFee());
        installOrderDTO.setFeeRate(installEntryDTO.getInstallFeeRate());
        installOrderDTO.setFeeDerateFlag(installEntryDTO.getInstallDerateMethod());
        installOrderDTO.setDerateFeeAmount(installEntryDTO.getInstallDerateValue());
        installOrderDTO.setTerm(installProConf.getTerm());
        installOrderDTO.setInstallmentAmount(installEntryDTO.getInstallAmount());
        installOrderDTO.setAcquireReferenceNo(installEntryDTO.getAcquireReferenceNo());
        LocalDate firstPostDate = calculateFirstPostDate(installOrderDTO,installProConf);
        installOrderDTO.setFirstPostDate(firstPostDate);
        installOrderDTO.setMerchantId(installEntryDTO.getMerchantId());
        installOrderDTO.setCustomerRegion(installEntryDTO.getCustomerRegion());
        installOrderDTO.setProductCode(installProductCode);
        installParameter.setInstallOrder(installOrderDTO);
        installParameter.setInstallProInfo(installProConf);
        installParameter.setInstallAccountTran(accountTran);
        //封装手续费计费组件
        InstallRateCalDTO installRateCal = getInstallRateCal(installParameter);
        //封装订单
        installParameter = packageInstallOrder(installRateCal, installParameter);
        //封装计划
        List<InstallPlan> installPlans = pakagePlan(installParameter);
        InstallTrialResDTO installTrialResDTO = new InstallTrialResDTO();
        installTrialResDTO.setInstallPlanList(BeanMapping.copyList(installPlans,InstallPlanDTO.class));
        InstallOrderDTO installOrder = installParameter.getInstallOrder();
        installTrialResDTO.setInstallmentAmount(installOrder.getInstallmentAmount());
        installTrialResDTO.setTotalFeeAmount(installOrder.getTotalFeeAmount());
        installTrialResDTO.setFeeRate(installOrder.getFeeRate());
        installTrialResDTO.setTerm(installOrder.getTerm());
        installTrialResDTO.setFirstTermAmount(installOrder.getFirstTermAmount());
        installTrialResDTO.setTermAmount(installOrder.getTermAmount());
        installTrialResDTO.setFeeTerm(installOrder.getFeeTerm());
        installTrialResDTO.setFirstTermFee(installOrder.getFirstTermFee());
        installTrialResDTO.setTermFee(installOrder.getTermFee());
        installTrialResDTO.setInstallCurrency(installOrder.getInstallmentCcy());
        installTrialResDTO.setDerateFeeAmount(installOrder.getDerateFeeAmount());
        installTrialResDTO.setDiscountRate(installOrder.getDiscountRate());
        return installTrialResDTO;
    }

    private LocalDate calculateFirstPostDate(InstallOrderDTO installOrderDTO,InstallProductInfoResDTO installProInfo){
        LocalDate firstPostDate;
        //延迟下账数/月
        Integer amountOfDelayed = installProInfo.getAmountOfDelayed();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
        if(Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrderDTO.getType())){
            //自动分期是批量入账 赋值today
            firstPostDate = organizationInfo.getToday();
        }else {
            //扣账日 0-交易日、1-账单日、2-还款日
            String billDateMethod = installProInfo.getBillDateMethod();
            firstPostDate  = organizationInfo.getNextProcessingDay();
            String accountManagementId = installOrderDTO.getAccountManagementId();
            if (null != accountManagementId && !"".equals(accountManagementId)) {
                AccountManagementInfo accountManagementInfo = installmentThreadLocalHolder.setAccountManagementInfo(accountManagementId);
//                if (accountManagementInfo == null) {
//                    accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
//                }
                List<ProductInfoResDTO> productInfoList = productInfoService.findProductInfo(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
                Short cycleDay = accountManagementInfo.getCycleDay();
                if ("0".equals(billDateMethod)) {
                    firstPostDate = organizationInfo.getNextProcessingDay();
                } else if ("1".equals(billDateMethod)) {
                    firstPostDate = calculateStatementDate(organizationInfo.getNextProcessingDay(), cycleDay);
                } else if ("2".equals(billDateMethod)) {
                    LocalDate statementDate = calculateStatementDate(organizationInfo.getNextProcessingDay(), cycleDay);
                    StatementProcessResDTO statementParam = statementProcessService.findByOrgAndTableId(accountManagementInfo.getOrganizationNumber(), productInfoList.get(0).getStatementProcessingTableId());
                    firstPostDate = statementDate.plusDays(statementParam.getDueDays());
                }
            }
        }
        if (amountOfDelayed != null){
            firstPostDate = firstPostDate.plusMonths(amountOfDelayed.longValue());
        }
        return firstPostDate;
    }



    /**
     * 分期手续费差异化定价规则校验
     *
     * @param dto 分期订单
     * @return Map
     * @date 2020/7/7
     */
    private Map<String, Object> installmentDiffPriceRuleCheck(InstallOrderDTO dto) {
        String ruleType = InstallmentConstant.INSTALLMENT_COMMISSION_DIFF_PRICE_RULE;
        TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher(ruleType, dto.getOrganizationNumber());
        if (ruleMatcher == null) {
            logger.info("Rule type is empty: ruleType={}", ruleType);
            return null;
        }
        logger.info("Rule factor data: cardNumber={}, installmentAmount={}, term={}",
                dto.getCardNumber(), dto.getInstallmentAmount(), dto.getTerm());
        String cardNumber = dto.getCardNumber();
        CardAuthorizationInfo cardAuth = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber,dto.getOrganizationNumber());
        String customerId = cardAuth.getPrimaryCustomerId();
        CardBasicInfo cardBasicInfo = cardBasicInfoMapper.selectByPrimaryKey(cardNumber,dto.getOrganizationNumber());
        String branchNumber = cardBasicInfo.getBranchNumber();
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(dto.getAccountManagementId());
        AcctProductInfoDTO byOrgAndProductNum = acctProductMainInfoService.findByOrgAndProductNum(accountManagementInfo.getOrganizationNumber(),accountManagementInfo.getProductNumber());
        if (byOrgAndProductNum == null){
            logger.warn("Account product parameter not found: organizationNumber={}, productNumber={}",
                    accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber());
            return null;
        }
        /* 规则因子数据*/
        Map<String, Object> inputMap = new HashMap<>(8);
        inputMap.put(InstallmentRuleConstant.INSTALLMENT_AMOUNT,dto.getInstallmentAmount());
        inputMap.put(InstallmentRuleConstant.MERCHANT_NUM,dto.getMerchantId());
        inputMap.put(InstallmentRuleConstant.TRANSACTION_TYPE_DETAIL,dto.getAuthTransactionTypeDetail());
        inputMap.put(InstallmentRuleConstant.TRANSACTION_TYPE_TOP,dto.getAuthTransactionTypeTop());
        inputMap.put(InstallmentRuleConstant.INSTALLMENT_PERIODS,dto.getTerm());
        inputMap.put(InstallmentRuleConstant.INSTALLMENT_PRODUCT,dto.getProductCode());
        inputMap.put(InstallmentRuleConstant.CUSTOMER_ID,customerId);
        inputMap.put(InstallmentRuleConstant.BRANCH_NUM,branchNumber);

        inputMap.put(InstallmentRuleConstant.GROUP_TYPE,dto.getGroupType());
        inputMap.put(InstallmentRuleConstant.CUSTOMER_REGION,dto.getCustomerRegion());
        inputMap.put("productAttribute",byOrgAndProductNum.getAcctProductMainInfoResDTO().getAttribute());

        if(null != accountManagementInfo){
            inputMap.put(InstallmentRuleConstant.ACCOUNT_PRODUCT_NUM,accountManagementInfo.getProductNumber());
        }
        inputMap.put(InstallmentRuleConstant.CARD_PRODUCT_NUMBER, cardAuth.getProductNumber());

        inputMap.put(InstallmentRuleConstant.MCC,dto.getMcc());

        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        inputMap.put(InstallmentRuleConstant.TRANSACTION_DATE,dto.getTransactionDate().format(fmt));
        DataInputDTO dataInput = new DataInputDTO(inputMap, ruleType);

        logger.info("Differential pricing rule input parameters: installmentAmount={}, merchantId={}, term={}",
                inputMap.get(InstallmentRuleConstant.INSTALLMENT_AMOUNT),
                inputMap.get(InstallmentRuleConstant.MERCHANT_NUM),
                inputMap.get(InstallmentRuleConstant.INSTALLMENT_PERIODS));
        Map<String,Object> ruleMap = ruleMatcher.execute(dataInput);
        logger.info("Differential pricing rule matching result: hasResult={}", ruleMap != null && !ruleMap.isEmpty());
        return ruleMap;
    }

    /**
     * 核心入账
     * @return Boolean true:入账成功 false:入账失败
     **/
    private Boolean coreAccount(InstallParameterDTO installParameter) {
        String createCreditFlag;
        String createTransactionCode;
        //划账手续费
        if(installParameter.getInstallProInfo().getTransferFee() != null && installParameter.getInstallProInfo().getTransferFee().compareTo(BigDecimal.ZERO) >0){
            //获取入账参数表
            String postingTransactionParmId = installParameter.getInstallProInfo().getPostingTransactionParmId();
            InstallAccountingTransParmResDTO installAccountTran = getInstallAccountTranByOrgNumAndTableId(installParameter.getInstallOrder().getOrganizationNumber(), postingTransactionParmId);
            //获取贷记交易标志
            //createCreditFlag = installAccountTran.getCreateCreditFlag();
            //获取贷记交易码
            createTransactionCode = installAccountTran.getTransferFeeCode();
            //调核心入账
            enterAccount(installParameter, createTransactionCode, TransactionSourceEnum.INNER_FEE);

        }

        //获取入账参数表
        String postingTransactionParmId = installParameter.getInstallProInfo().getPostingTransactionParmId();
        InstallAccountingTransParmResDTO installAccountTran = getInstallAccountTranByOrgNumAndTableId(installParameter.getInstallOrder().getOrganizationNumber(), postingTransactionParmId);
        //获取贷记交易标志
        createCreditFlag = installAccountTran.getCreateCreditFlag();
        //获取贷记交易码
        createTransactionCode = installAccountTran.getCreateTransactionCode();
        //调核心入账
        if ("Y".equals(createCreditFlag)) {
            enterAccount(installParameter, createTransactionCode, TransactionSourceEnum.LOCAL);

        }
        return true;
    }

    public void enterAccount(InstallParameterDTO installParameter,String createTransactionCode,
                             TransactionSourceEnum transactionSourceEnum){
        RecordedBO recorded = getRecorded(installParameter.getInstallOrder(),
                createTransactionCode,
                installParameter.getOriginTransactionId(),
                installParameter.getInstallProInfo());
        recorded.setTxnTransactionSource(transactionSourceEnum.getCode());

        try {
            //添加分期额度处理模式
            recorded.setInstallmentLimitProcessIndicator(installParameter.getInstallProInfo().getLimitProcessMode());
            CustReconciliationControlDTO custReconciliationDTO = installParameter.getCustReconciliationDTO();
            if(InstallmentTypeEnum.AUTO_INSTALL.getCode().equals(recorded.getInstallmentType())){
                //自动分期走批量入账，入账日期赋值today
                //入账方式对超长免息期，实际免息结息日、实际出账单日有影响
                recorded.setTxnPostMethod(PostMethodEnum.BATCH.getCode());
                // 自动分期调入账（仅批量使用！！！）
                txnRecordedService.txnRecordedBatch(recorded);
            }else {
                TransRecordResultBO transRecordResultBO = txnRecordedService.txnRecorded(recorded);
                if(transRecordResultBO.getResult() == 1){

                    logger.error("Installment credit posting rejected: result={}", transRecordResultBO.getResult());
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ADD_INST_ORDER_CALL_CORE_BOOKS_FAULT);
                }
            }

            installParameter.setCustReconciliationDTO(custReconciliationDTO);
        } catch (Exception e) {
            logger.error("Failed to call core accounting for new order credit and transfer fee", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ADD_INST_ORDER_CALL_CORE_BOOKS_FAULT);
        }
    }

    /**
     * 批量插入计划表
     *
     * @param installPlans 分期计划
     * @return int 影响行数
     **/
    @BatchSharedAnnotation
    private int insertPlanBatch(List<InstallPlan> installPlans) {
        try {
            List<InstallPlanDTO> installPlanDtos = BeanMapping.copyList(installPlans, InstallPlanDTO.class);
            if (CustAccountBO.isBatch()) {
                installPlanDtos.forEach(x -> CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallPlan(x, false));
                return 1;
            } else {
                int resultPlan = installPlanService.insertInstallPlanBatch(installPlanDtos);
                if (resultPlan < 0) {
                    logger.error("Failed to batch insert installment plans into database: resultPlan={}", resultPlan);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
                }
                return resultPlan;
            }
        } catch (Exception e) {
            logger.error("Exception occurred while batch inserting installment plans into database", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
        }
    }

    /**
     * 手续费计算参数
     *
     * @param installParameter 分期订单的参数
     * @return InstallRateCalDTO
     **/
    public InstallRateCalDTO getInstallRateCal(InstallParameterDTO installParameter) {
        InstallRateCalDTO installRateCalDTO = new InstallRateCalDTO();
        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
        //费用代码
        String feeCodeId = installParameter.getInstallProInfo().getFeeCodeId();
        //手续费收取方式
        String feeReceiveFlag = installParameter.getInstallProInfo().getFeeReceiveFlag();
        installRateCalDTO.setOrganizationNumber(installOrderDTO.getOrganizationNumber());
        installRateCalDTO.setInstallmentPriceFlag(installOrderDTO.getInstallPriceFlag());
        installRateCalDTO.setInstallmentTotalFee(installOrderDTO.getTotalFeeAmount());
        installRateCalDTO.setInstallmentFeeRate(installOrderDTO.getFeeRate());
        installRateCalDTO.setInstallmentDerateMethod(installOrderDTO.getFeeDerateFlag());
        installRateCalDTO.setInstallmentDerateValue(installOrderDTO.getDerateFeeAmount());
        installRateCalDTO.setInstallmentTerm(installOrderDTO.getTerm());
        installRateCalDTO.setInstallmentAmount(installOrderDTO.getInstallmentAmount());
        installRateCalDTO.setInstallmentFeeCodeBean(feeCodeId);
        installRateCalDTO.setFeeFlag(feeReceiveFlag);
        //本金减免相应字段设置

        if (StringUtils.isBlank(installRateCalDTO.getFeeFlag())) {
            logger.error("Fee collection method does not exist");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_FEE_FLAG_NOT_NULL_FAULT);
        }

        return installRateCalDTO;
    }

    /**
     * 必输项校验
     * @param installOrderDTO 分期订单
     */
    public void mustPassCheck(InstallOrderDTO installOrderDTO) {
        if (installOrderDTO == null) {
            logger.error("Installment order creation failed: order parameter entity is null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.PA_CR);
        }
        //机构号
        String organizationNumber = installOrderDTO.getOrganizationNumber();
        if (StringUtils.isBlank(organizationNumber)) {
            logger.error("Installment order creation failed: organization number is required, organizationNumber={}", organizationNumber);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.RE_OR);
        }
        //管理账号
//        String accountManagementId = installOrderDTO.getAccountManagementId();
//        if (StringUtils.isBlank(accountManagementId)) {
//            logger.error("Installment order creation failed: account management id is required, accountManagementId={}", accountManagementId);
//            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT,"分期订单创建 管理账号必输");
//        }
        //卡号
        String cardNumber = installOrderDTO.getCardNumber();
        if (StringUtils.isBlank(cardNumber)) {
            logger.error("Installment order creation failed: card number is required, cardNumber={}", cardNumber);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CR_CA);
        }
        //交易日期
        LocalDateTime transactionDate = installOrderDTO.getTransactionDate();
        if (transactionDate == null) {
            logger.error("Installment order creation failed: transaction date is required");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CR_TD);
        }
        //分期授权码
        String authorizationCode = installOrderDTO.getAuthorizationCode();
        if (StringUtils.isBlank(authorizationCode)) {
            logger.error("Installment order creation failed: authorization code is required, authorizationCode={}", authorizationCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CR_AC);
        }
        //分期货币
        String installmentCcy = installOrderDTO.getInstallmentCcy();
        if (StringUtils.isBlank(installmentCcy)) {
            logger.error("Installment order creation failed: installment currency is required, installmentCcy={}", installmentCcy);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT,  InstallRepDetailEnum.CR_CU);
        }
        //分期金额必输
        BigDecimal installmentAmount = installOrderDTO.getInstallmentAmount();

        //最小授权金额
        BigDecimal minAuthAmount = installOrderDTO.getMinAuthAmount();
        //最大授权额
        BigDecimal maxAuthAmount = installOrderDTO.getMaxAuthAmount();

        if (installmentAmount == null) {
            logger.error("Installment order creation failed: installment amount is required");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT,  InstallRepDetailEnum.CR_AM);
        }
        if (minAuthAmount != null && installmentAmount.compareTo(minAuthAmount) < 1){
            logger.error("Installment order creation failed: installment amount must be greater than minimum authorization amount, installmentAmount={}, minAuthAmount={}",
                    installmentAmount, minAuthAmount);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR,  InstallRepDetailEnum.CR_AM);
        }

        if (maxAuthAmount != null && installmentAmount.compareTo(maxAuthAmount) > 0){
            logger.error("Installment order creation failed: installment amount must be less than or equal to maximum authorization amount, installmentAmount={}, maxAuthAmount={}",
                    installmentAmount, maxAuthAmount);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR,  InstallRepDetailEnum.CR_SZ);
        }

    }

    /**
     * 封装计划
     *
     * @param installParameter 创建分期订单的参数信息
     * @return List<InstallPlan>
     */
    public List<InstallPlan> pakagePlan(InstallParameterDTO installParameter) {
        FeeTypeDTO feeTypeDTO = installParameter.getFeeTypeDTO();
        //手续费收取方式
        String feeReceiveFlag = installParameter.getInstallProInfo().getFeeReceiveFlag();
        //产品周期
        String cycle = installParameter.getInstallProInfo().getCycle();
        //分期期数
        Integer term;
        if (PaymentWayEnum.FIRST_COST_LATER_COST.getCode().equals(installParameter.getInstallOrder().getPaymentWay())) {
            term = installParameter.getInstallOrder().getFeeTerm();
        } else {
            term = installParameter.getInstallOrder().getTerm();
        }
        //解决ANYTXNCMBC-424,先费后本模式，手续费为期初一次性收取或期末一次性收取
        if (PaymentWayEnum.FIRST_COST_LATER_COST.getCode().equals(installParameter.getInstallOrder().getPaymentWay())
                && (FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeReceiveFlag)
                || FeeReceiveFagEnum.END_ONE_TIME.getCode().equals(feeReceiveFlag))) {

            String productCode = installParameter.getInstallOrder().getProductCode();
            InstallProductInfoResDTO installProductInfoResDTO = installProductInfoService.findByIndex(installParameter.getInstallOrder().getOrganizationNumber(), productCode);
            term = installProductInfoResDTO.getTerm();
        }


        //1.计算利息摊销
        List<BigDecimal>  calculateInterestAmortize = calculateInterestAmortize(installParameter, term);

        //2.计算费用摊销
        ImmutablePair<BigDecimal, BigDecimal> calculateFeeAmortize =  calculateFeeAmortize(
                term, feeReceiveFlag, feeTypeDTO, installParameter);


        //封装分期计划表
        List<InstallPlan> arrayList = new ArrayList<>();
        LocalDate termPostDate = installParameter.getInstallOrder().getFirstPostDate();
        if (termPostDate == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ED_E);
        }
        //中行POC 不收取费用的期数
        Integer unReceiveFeeTerm = feeTypeDTO.getUnReceiveFeeTerm() == null ? 0 : feeTypeDTO.getUnReceiveFeeTerm();
        if (unReceiveFeeTerm.compareTo(term) >= 0) {
            logger.error("Number of terms without fee collection is greater than or equal to installment terms");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR, InstallRepDetailEnum.PE_GT_IN);
        }
        //中行poc 不收取费用的期数
        Integer firstReceiveFeeTerm = unReceiveFeeTerm + 1;
        //中行poc
        InstallProductInfoResDTO installProInfo = installParameter.getInstallProInfo();
        //尾款方式 0-余数放首期、1-余数放尾期
        String balanceMethod = installProInfo.getBalanceMethod();



        //减免期数
        Integer deraTerm = feeTypeDTO.getDerateTerm();

        for (int i = 1; i <= term; i++) {

            InstallPlan installPlan = new InstallPlan();

            installPlan.setOrderId(installParameter.getInstallOrder().getOrderId());
            //机构号
            installPlan.setOrganizationNumber(installParameter.getInstallOrder().getOrganizationNumber());
            //状态
            installPlan.setTermStutus("N");
            installPlan.setAbsStatus(installParameter.getInstallOrder().getAbsStatus());

            //本金减免金额
            installPlan.setPrincipalDerateFeeAmount(Optional.ofNullable(feeTypeDTO.getPrincipalDerateFeeAmount()[i-1]).orElse(BigDecimal.ZERO));

            //第一期特殊处理
            if (i == 1) {
                //入账日期
                installPlan.setTermPostDate(termPostDate);

                //尾款方式 0-余数放首期、1-余数放尾期
                if ("0".equals(balanceMethod) || term == 1) {
                    installPlan.setTermAmount(feeTypeDTO.getFirstAmount());
                } else if ("1".equals(balanceMethod)) {
                    installPlan.setTermAmount(feeTypeDTO.getEashAmount());
                }

                //中行poc
                if (unReceiveFeeTerm.compareTo(1) >= 0) {
                    installPlan.setFeeAmount(BigDecimal.ZERO);
                } else {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod) || term == 1 || FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeReceiveFlag)) {
                        installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                    } else if ("1".equals(balanceMethod)) {
                        installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                    }
                }
                if (deraTerm >= 1) {
                    installPlan.setDerateAmount(feeTypeDTO.getDerateFeeAmount()[i - 1]);
                } else {
                    installPlan.setDerateAmount(BigDecimal.ZERO);
                }
                installPlan.setAmortizeFee(calculateFeeAmortize.getLeft());
                installPlan.setTerm(i);
            } else {
                if (Objects.equals(InstallCycleEnum.YEAR.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusYears(i - 1L));
                } else if (Objects.equals(InstallCycleEnum.HALF_YEAR.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusMonths(6L * (i - 1)));
                } else if (Objects.equals(InstallCycleEnum.SEASON.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusMonths(3L * (i - 1)));
                } else if (Objects.equals(InstallCycleEnum.MOUTH.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusMonths(i - 1L));
                } else if (Objects.equals(InstallCycleEnum.DAY.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusDays(i - 1L));
                }
                if (i == term && PaymentWayEnum.FIRST_COST_LATER_COST.getCode().equals(installParameter.getInstallOrder().getPaymentWay())) {
                    installPlan.setTermAmount(installParameter.getInstallOrder().getInstallmentAmount());
                } else {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod)) {
                        installPlan.setTermAmount(feeTypeDTO.getEashAmount());
                    } else if ("1".equals(balanceMethod)) {

                        if (i == term) {
                            installPlan.setTermAmount(feeTypeDTO.getFirstAmount());
                        } else {
                            installPlan.setTermAmount(feeTypeDTO.getEashAmount());

                        }

                    }
                }

                if (firstReceiveFeeTerm.compareTo(i) == 0) {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod)) {
                        installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                    } else if ("1".equals(balanceMethod)) {
                        if (i == term) {
                            installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                        } else {
                            installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                        }

                    }
                } else if (firstReceiveFeeTerm.compareTo(i) > 0) {
                    installPlan.setFeeAmount(BigDecimal.ZERO);
                } else {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod)) {
                        installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                    } else if ("1".equals(balanceMethod)) {
                        if (i == term && !"F".equals(feeReceiveFlag)) {
                            installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                        } else {
                            installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                        }

                    }
                }

                if (i <= feeTypeDTO.getDerateFeeAmount().length) {
                    installPlan.setDerateAmount(feeTypeDTO.getDerateFeeAmount()[i - 1]);
                } else {
                    installPlan.setDerateAmount(BigDecimal.ZERO);
                }

                installPlan.setAmortizeFee(calculateFeeAmortize.getRight());
                installPlan.setTerm(i);
            }
            //中行POC E-期末一次性收取
            if (FeeReceiveFagEnum.END_ONE_TIME.getCode().equals(feeReceiveFlag) && i == term) {
                installPlan.setFeeAmount(feeTypeDTO.getTotalCost());
            }
            installPlan.setAmortizeInterest(calculateInterestAmortize.get(i - 1));
            installPlan.setCreateTime(LocalDateTime.now());
            installPlan.setUpdateTime(LocalDateTime.now());
            installPlan.setUpdateBy("admin");
            installPlan.setVersionNumber(1L);
            arrayList.add(installPlan);
        }
        return arrayList;
    }

    private List<BigDecimal> calculateInterestAmortize(InstallParameterDTO installParameter,
                                                       Integer term) {

        logger.info("Total interest amount: {}", installParameter.getInstallOrder().getInstallmentTotalInterest());
        if (installParameter.getInstallOrder().getInstallmentTotalInterest() == null
                || installParameter.getInstallOrder().getInstallmentTotalInterest().compareTo(BigDecimal.ZERO) == 0){
            BigDecimal[] arrayList = new BigDecimal[term];
            Arrays.fill(arrayList,BigDecimal.ZERO);

            return Arrays.asList(arrayList);
        }


        InstallmentInterestDTO interestDTO = InstallmentInterestDTO
                .InstallmentInterestDTOBuilder
                .anInstallmentInterestDTO()
                .withInterestAmount(installParameter.getInstallOrder().getInstallmentTotalInterest())
                .withTerm(term)
                .build();

        List<BigDecimal> monthlyInterestAmount = new Rule78Interest().getInterestResult(interestDTO).getMonthlyInterestAmount();

        logger.info("Interest amortization amount: size={}", monthlyInterestAmount.size());
        return monthlyInterestAmount;
    }


    private ImmutablePair<BigDecimal, BigDecimal> calculateFeeAmortize(Integer term,
                                                                       String feeReceiveFlag,
                                                                       FeeTypeDTO feeTypeDTO,
                                                                       InstallParameterDTO installParameter) {
        BigDecimal termdb = new BigDecimal(term);
        BigDecimal termAmortize = BigDecimal.ZERO;
        BigDecimal firstAmortize = BigDecimal.ZERO;
        //手续费收取方式为F-期初一次性收取 E-期末一次性收取
        if (FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeReceiveFlag) || FeeReceiveFagEnum.END_ONE_TIME.getCode().equals(feeReceiveFlag)) {
            /*
               手续费分摊处理
               首期减免费用
               总的摊销费用 = 首期总的费用-首期减免费用
             */

            BigDecimal totalAmortize;
            if (!Objects.equals(InstallDerateMethodEnum.EACH_DISCOUNT.getCode(), feeTypeDTO.getDerateMethod())) {
                totalAmortize = feeTypeDTO.getTotalCost().subtract(feeTypeDTO.getDerateFeeAmount()[0]);
            } else {
                totalAmortize = feeTypeDTO.getTotalCost();
            }

            //光大POC, 如果分期手续费交易码的增值税标志为0：拆税，则用交易码读取增值税参数表获取增值税税率
            BigDecimal vatRate = BigDecimal.ZERO;
            boolean vatFlag = false;
            if (installParameter.getInstallAccountTran() != null) {
                String feeCode = installParameter.getInstallAccountTran().getFeeTransactionCode();
                TransactionCodeResDTO transactionCode = findParamTransactionCode(feeCode,
                        installParameter.getInstallOrder().getOrganizationNumber());
                if (transactionCode != null) {
                    String taxFlag = transactionCode.getTaxFlag();
                    if ("0".equals(taxFlag)) {
//                        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
//                        if (installOrderDTO != null && installOrderDTO.getAccountManagementId() != null) {
//                            AccountManagementInfoDTO accountManagementInfoDTO = BeanMapping.copy(accountManagementInfoMapper.selectByPrimaryKey(installOrderDTO.getAccountManagementId()), AccountManagementInfoDTO.class);
//                            因为管理账户id为空 所以查询增值税率参数表的BRANCHID条件暂时写死
                        TPmsGlamtr tPmsGlamtr = tPmsGlamtrSelfMapper.selectByTxnCodeAndFinanceStatus(
                                installParameter.getInstallOrder().getOrganizationNumber(), "110110", feeCode);
                        if (tPmsGlamtr != null && tPmsGlamtr.getTaxRate() != null) {
                            //成功取得增值税税率
                            vatRate = new BigDecimal(tPmsGlamtr.getTaxRate());
                            vatFlag = true;
                        }
                    }
                }
            }

            if (vatFlag) {
                BigDecimal tenThousand = new BigDecimal("10000");
                /*
                 * 正向公式是：价金额+税金额=总额，假设6%税率
                 * 税金额=价金额*6%
                 * 推导：
                 * 价金额+价金额*6%=总金额，
                 * 价金额=总金额/(1+6%)
                 * 四舍五入
                 */
                totalAmortize = totalAmortize.divide(vatRate.divide(tenThousand).add(BigDecimal.ONE), 2, BigDecimal.ROUND_HALF_UP);
            }

            //每期摊销费用 = 总的摊销费用/分期期数
            termAmortize = totalAmortize.divide(termdb, 2, BigDecimal.ROUND_DOWN);
            //第一期摊销费用 =总的摊销费用-每期摊销费用*（分期期数 - 1）
            firstAmortize = totalAmortize.subtract(termAmortize.multiply(termdb.subtract(BigDecimal.ONE)));
        }
        return ImmutablePair.of(firstAmortize, termAmortize);

    }

    /**
     * 封装订单
     *
     * @param installRateCal 费率计算参数
     * @param installParameter 费率计算参数
     * @return InstallParameterDTO
     */
    public InstallParameterDTO packageInstallOrder(InstallRateCalDTO installRateCal,
                                                   InstallParameterDTO installParameter) {
        //手续费计算组件
        installRateCal.setInstallParameterDTO(installParameter);
        FeeTypeDTO feeTypeDTO = installFeeCalculationService.installmentFeeCalculation(installRateCal);
        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
        //每期本金计算
        InstallOrder installOrder = principalCalculation(installParameter, feeTypeDTO);

        //封装分期订单信息表
        installOrder.setOrderId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        installOrder.setCustomerRegion(installOrderDTO.getCustomerRegion());
        installOrder.setCustomerId(installOrderDTO.getCustomerId());
        installOrder.setBranchNumber(installOrderDTO.getBranchNumber());
        installOrder.setGroupType(installOrderDTO.getGroupType());
        installOrder.setOrganizationNumber(installOrderDTO.getOrganizationNumber());
        installOrder.setAccountManagementId(installOrderDTO.getAccountManagementId());
        installOrder.setCardNumber(installOrderDTO.getCardNumber());
        installOrder.setProductCode(installOrderDTO.getProductCode());
        if (Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrderDTO.getType())) {
            //自动分期是批量入账 赋值today
            installOrder.setTransactionDate(installOrderDTO.getFirstPostDate().atTime(LocalTime.now()));
        } else {
            installOrder.setTransactionDate(installOrderDTO.getTransactionDate());
        }
        installOrder.setAcquireReferenceNo(installOrderDTO.getAcquireReferenceNo());
        installOrder.setAuthorizationCode(installOrderDTO.getAuthorizationCode());
        installOrder.setInstallmentCcy(installOrderDTO.getInstallmentCcy());
        installOrder.setType(installOrderDTO.getType());
        installOrder.setStatus(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode());
        //订单状态变更日期
        installOrder.setStatusUpdateDate(LocalDate.now());
        //首次入账日期 系统当前业务日期
        installOrder.setFirstPostDate(installOrderDTO.getFirstPostDate());
        //已入账本金期数
        installOrder.setPostedTerm(0);
        //分期金额
        installOrder.setInstallmentAmount(installOrderDTO.getInstallmentAmount());
        //未入账金额
        installOrder.setUnpostedAmount(installOrderDTO.getInstallmentAmount());
        //手续费计算总的费用金额
        //总费用金额
        installOrder.setTotalFeeAmount(feeTypeDTO.getTotalCost());

        if (feeTypeDTO.getTotalCost().compareTo(BigDecimal.ZERO) == 0){
            installOrder.setFeeTerm(0);
        }

        //未入账费用金额
        installOrder.setUnpostedFeeAmount(feeTypeDTO.getTotalCost());
        //已入账费用期数
        installOrder.setPostedFeeTerm(0);
        //还款方式
        installOrder.setPaymentWay(installParameter.getInstallProInfo().getPaymentWay());
        //手续费收取方式
        installOrder.setFeeFlag(installParameter.getInstallProInfo().getFeeReceiveFlag());
        //费率
        installOrder.setFeeRate(feeTypeDTO.getFeeRate());
        //手续费减免方式
        installOrder.setFeeDerateFlag(feeTypeDTO.getDerateMethod());

        //费用减免期数, 减免金额> 0 的期数
        installOrder.setDerateTerm(feeTypeDTO.getDerateTerm());


        //费用减免总金额
        BigDecimal feeReduce = feeTypeDTO.getDerateFeeAmount() == null ? BigDecimal.ZERO :
                Arrays.stream(feeTypeDTO.getDerateFeeAmount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        installOrder.setDerateFeeAmount(feeReduce);

        if (Objects.equals(InstallDerateMethodEnum.EACH_DISCOUNT.getCode(), feeTypeDTO.getDerateMethod())) {
            installOrder.setDiscountRate(feeTypeDTO.getDerateValue());
        }


        //本金减免总金额
        Optional<BigDecimal> prinReduce = Arrays.stream(feeTypeDTO.getPrincipalDerateFeeAmount()).reduce(BigDecimal::add);
        installOrder.setPrincipalDerateTotalAmount(prinReduce.orElse(BigDecimal.ZERO));
        //本金减免期数
        installOrder.setPrincipalDerateTerm(feeTypeDTO.getPrincipalDerateTerm());
        //本金累计减免金额
        installOrder.setPrincipalDerateAmount(BigDecimal.ZERO);

        //手续费累计收取金额
        installOrder.setTotalReceivedFee(BigDecimal.ZERO);
        //手续费累计减免金额
        installOrder.setTotalDerateFee(BigDecimal.ZERO);
        //提前还款违约金收取金额
        installOrder.setReceivedPenatlyAmount(BigDecimal.ZERO);
        //累计退货金额
        installOrder.setTotalReturnedAmount(BigDecimal.ZERO);
        //分期占用额度节点
        installOrder.setLimitCode(installOrderDTO.getLimitCode());
        //商户号
        installOrder.setMerchantId(installOrderDTO.getMerchantId());
        //商户分类
        installOrder.setMcc(installOrderDTO.getMcc());
        installOrder.setTransactionDesc(installOrderDTO.getTransactionDesc());
        //原始交易ID
        installOrder.setOriginTransactionId(installOrderDTO.getOriginTransactionId());
        installOrder.setCreateTime(LocalDateTime.now());
        installOrder.setUpdateTime(LocalDateTime.now());
        installOrder.setUpdateBy("admin");
        installOrder.setVersionNumber(1L);
        installOrder.setGlobalFlowNumber(installOrderDTO.getGlobalFlowNumber());
        installOrder.setAbsStatus(installOrderDTO.getAbsStatus());
        installOrder.setAbsProductCode(installOrderDTO.getAbsProductCode());
        installOrder.setMerchantName(installOrderDTO.getMerchantName());
        installOrder.setRetrievalReferenceNumber(installOrderDTO.getRetrievalReferenceNumber());
        installOrder.setInstallmentTotalInterest(installOrderDTO.getInstallmentTotalInterest());
        logger.info("Installment order info: orderId={}, installmentAmount={}, term={}",
                installOrder.getOrderId(), installOrder.getInstallmentAmount(), installOrder.getTerm());
        installParameter.setInstallOrder(BeanMapping.copy(installOrder, installOrderDTO.getClass()));
        installParameter.setFeeTypeDTO(feeTypeDTO);
        return installParameter;
    }

    /**
     * 如果分期费用代码中计费方式为一次性收取则
     * 总服务费 - 总减免费用 直接入账 并且 分期计划中不再收取服务费
     * @param installParameter 分期参数
     */
    private void feeAmountAccount(InstallParameterDTO installParameter) {

        InstallFeeCodeInfoResDTO installFeeCode = installParameter.getInstallFeeCode();

        if (!Objects.equals(StatusEnum.EFFECTIVE.getCode(),installFeeCode.getOneTimeChargeFlag())){
            return;
        }

        BigDecimal feeAmount = installParameter.getInstallOrder().getTotalFeeAmount()
                .subtract(Optional.ofNullable(installParameter.getInstallOrder().getDerateFeeAmount()).orElse(BigDecimal.ZERO));

        if (feeAmount.compareTo(BigDecimal.ZERO) <= 0){
            return;
        }

        RecordedBO recorded = new RecordedBO();
        //卡号
        recorded.setTxnCardNumber(installParameter.getInstallOrder().getCardNumber());
        //全局业务流水号
        recorded.setTxnGlobalFlowNumber(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        //入账方式
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        //拒绝重入账标志
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        //冲减交易费用标识
        recorded.setTxnReverseFeeIndicator(ReverseFeeIndicatorEnum.NO.getCode());

        String postingTransactionParmId = installParameter.getInstallProInfo().getPostingTransactionParmId();
        InstallAccountingTransParmResDTO installAccountTran = getInstallAccountTranByOrgNumAndTableId(installParameter.getInstallOrder().getOrganizationNumber(), postingTransactionParmId);

        //交易码
        recorded.setTxnTransactionCode(installAccountTran.getFeeTransactionCode());
        //交易来源
        recorded.setTxnTransactionSource(TransactionSourceEnum.INNER_FEE.getCode());
        //交易描述
        recorded.setTxnTransactionDescription(installParameter.getInstallOrder().getTransactionDesc() + " PROCESSING FEE");

        //交易日期
        recorded.setTxnTransactionDate(installParameter.getInstallOrder().getTransactionDate());
        //交易金额
        recorded.setTxnTransactionAmount(feeAmount);
        //交易币种,交易级账户的币种（currency）
        recorded.setTxnTransactionCurrency(installParameter.getInstallOrder().getInstallmentCcy());
        //入账日期
        recorded.setTxnBillingDate(LocalDate.now());
        //入账金额
        recorded.setTxnBillingAmount(feeAmount);
        //入账币种
        recorded.setTxnBillingCurrency(installParameter.getInstallOrder().getInstallmentCcy());
        //清算金额
        recorded.setTxnSettlementAmount(feeAmount);
        //清算币种
        recorded.setTxnSettlementCurrency(installParameter.getInstallOrder().getInstallmentCcy());
        //授权匹配标志,0=未匹配授权
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //是否恢复授权占用额度标志,N:不需要
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        //单双信息赋值
        recorded.setMessageIndicator(MessageIndicatorEnum.OTHER_INDICATOR.getCode());

        recorded.setTxnInstallmentOrderId(installParameter.getInstallOrder().getOrderId());
        recorded.setTxnInstallmentTerm("1");

        TransRecordResultBO recordResultBO = txnRecordedService.txnRecorded(recorded);
        logger.info("One-time fee collection result: result={}", recordResultBO.getResult());
        if (recordResultBO.getResult() == 1) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.S_TRANS_REJECT);
        }

        if (installParameter.getFeeTypeDTO().getDerateFeeAmount() != null){
            Arrays.fill(installParameter.getFeeTypeDTO().getDerateFeeAmount(),BigDecimal.ZERO);
        }
        //一次性收取费用不再设置每期的费用数据
        installParameter.getFeeTypeDTO().setEashPayment(BigDecimal.ZERO);
        installParameter.getFeeTypeDTO().setFirstPayment(BigDecimal.ZERO);
        installParameter.getInstallOrder().setUnpostedFeeAmount(BigDecimal.ZERO);
        installParameter.getInstallOrder().setFirstTermFee(BigDecimal.ZERO);
        installParameter.getInstallOrder().setFeeTerm(0);
        installParameter.getInstallOrder().setTermFee(BigDecimal.ZERO);
    }

    /**
     * 每期本金计算
     *
     * @param installParameter 分期参数
     * @param feeTypeDTO       费用
     * @return InstallOrder
     */
    private InstallOrder principalCalculation(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {
        InstallOrder installOrder = new InstallOrder();

        //获取分期费用代码
        InstallFeeCodeInfoResDTO installFeeCode = installParameter.getInstallFeeCode();
        //本金减免方式
        feeTypeDTO.setPrincipalReliefMethod(installFeeCode.getPrincipalRelief());

        //还款方式
        String paymentWay = installParameter.getInstallProInfo().getPaymentWay();
        //手续费收取方式
        String feeReceiveFlag = installParameter.getInstallProInfo().getFeeReceiveFlag();
        //每期本金
        BigDecimal termAmount;
        //首期本金
        BigDecimal firstTermAmount;
        //分期金额
        BigDecimal installmentAmount = installParameter.getInstallOrder().getInstallmentAmount();
        //分期期数
        Integer term = installParameter.getInstallOrder().getTerm();
        BigDecimal termdb = new BigDecimal(term);
        //中行poc
        InstallProductInfoResDTO installProInfo = installParameter.getInstallProInfo();
        //尾款方式 0-余数放首期、1-余数放尾期
        String balanceMethod = installProInfo.getBalanceMethod();

        //还款方式为A-等费等本
        if (Objects.equals(PaymentWayEnum.EQUAL_COST_EQUAL_CAPITAL.getCode(), paymentWay)) {
            //每期本金 = 分期金额/分期期数
            termAmount = installmentAmount.divide(termdb, 0, BigDecimal.ROUND_DOWN);
            //首期本金 =分期金额-每期本金 * (分期期数-1)
            firstTermAmount = installmentAmount
                    .subtract(termAmount.multiply(termdb.subtract(BigDecimal.ONE)));
            feeTypeDTO.setEashAmount(termAmount);
            feeTypeDTO.setFirstAmount(firstTermAmount);
            //尾款方式 0-余数放首期、1-余数放尾期
            if ("0".equals(balanceMethod)) {
                installOrder.setTermAmount(termAmount);
                installOrder.setFirstTermAmount(firstTermAmount);
                //本金减免金额
            } else {
                installOrder.setTermAmount(termAmount);
                installOrder.setFirstTermAmount(termAmount);
            }

            setEachPrincipalTermArray(termAmount,firstTermAmount, installParameter, feeTypeDTO);
            installOrder.setTerm(term);
        }

        //还款方式为F-先费后本
        if (Objects.equals(PaymentWayEnum.FIRST_COST_LATER_COST.getCode(), paymentWay)) {
            //每期本金=0
            termAmount = BigDecimal.ZERO;
            feeTypeDTO.setEashAmount(termAmount);
            feeTypeDTO.setFirstAmount(termAmount);
            installOrder.setTermAmount(termAmount);
            installOrder.setFirstTermAmount(termAmount);
            installOrder.setTerm(1);

            setEachPrincipalTermArray(BigDecimal.ZERO,installmentAmount, installParameter, feeTypeDTO);
        }

        principalRelief(installParameter,feeTypeDTO);

        //每期费用计算
        //手续费收取方式为I-分期收取
        if (Objects.equals(FeeReceiveFagEnum.INSTALMENT.getCode(), feeReceiveFlag)) {
            //尾款方式 0-余数放首期、1-余数放尾期
            if ("0".equals(balanceMethod)) {
                //每期手续费 = 每期手续费
                installOrder.setTermFee(feeTypeDTO.getEashPayment());
                //首期手续费 = 首期手续费
                installOrder.setFirstTermFee(feeTypeDTO.getFirstPayment());
            } else {
                //每期手续费 = 每期手续费
                installOrder.setTermFee(feeTypeDTO.getEashPayment());
                //首期手续费 = 每期手续费
                installOrder.setFirstTermFee(feeTypeDTO.getEashPayment());
            }

            installOrder.setFeeTerm(term);
        }

        //手续费收取方式为F-期初一次性收取
        if (Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeReceiveFlag)) {
            //每期手续费 = 0
            installOrder.setTermFee(BigDecimal.ZERO);
            //首期手续费 = 分期总的手续费
            installOrder.setFirstTermFee(feeTypeDTO.getTotalCost());
            installOrder.setFeeTerm(1);
        }
        //手续费收取方式为F-期末一次性收取
        if (Objects.equals(FeeReceiveFagEnum.END_ONE_TIME.getCode(), feeReceiveFlag)) {
            //每期手续费 = 0
            installOrder.setTermFee(BigDecimal.ZERO);
            //首期手续费 = 0
            installOrder.setFirstTermFee(BigDecimal.ZERO);
            installOrder.setFeeTerm(1);
        }
        logger.info("Installment fee type info: totalCost={}, firstAmount={}, eashAmount={}",
                feeTypeDTO.getTotalCost(), feeTypeDTO.getFirstAmount(), feeTypeDTO.getEashAmount());
        return installOrder;
    }


    /**
     * 本金减免金额计算
     * @param feeTypeDTO 手续费计算涉及参数
     * @param installParameter 分期参数
     */
    private void principalRelief(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //本金减免方式
        String principalReliefMethod = feeTypeDTO.getPrincipalReliefMethod();


        //不减免
        if (Objects.equals(PrincipalReductionMethodEnum.NO_REDUCTION.getCode(),principalReliefMethod)){
            noPrincipalBreak(feeTypeDTO);
        }
        //减免方式为1-减免期数
        if (Objects.equals(PrincipalReductionMethodEnum.BREAKS_TEM.getCode(),principalReliefMethod)){
            principalBreakTerm(installParameter,feeTypeDTO);
        }
        //减免方式2-减免金额
        else if (Objects.equals(PrincipalReductionMethodEnum.CREDIT_AMOUNT.getCode(),principalReliefMethod)){

            principalBreaksAmount(installParameter,feeTypeDTO);
        }
        //减免方式3-减免期数及金额
        else if (Objects.equals(PrincipalReductionMethodEnum.PERIODS_AND_AMOUNT.getCode(),principalReliefMethod)){

            principalBreaksAmountAndTerm(installParameter,feeTypeDTO);
        }

    }




    /**
     * 不减免
     * @param feeTypeDTO 手续费计算涉及参数
     *
     * */
    private void noPrincipalBreak(FeeTypeDTO feeTypeDTO) {

        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);
        //分期减免期数
        feeTypeDTO.setPrincipalDerateTerm(0);
        //本金减免费用金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);

    }

    /**
     * 减免期数
     * @param feeTypeDTO 手续费计算涉及的参数
     * @param installParameter 分期参数
     *
     */
    private void principalBreakTerm(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //本金期数减免值
        BigDecimal principalTermValue = installParameter.getInstallFeeCode().getPrincipalTermValue();

        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);

        int len = principalTermValue.intValue() > installParameter.getInstallOrder().getTerm()
                ? installParameter.getInstallOrder().getTerm() : principalTermValue.intValue();

        //每期-本金分期费用金额
        for (int i = 0; i < len; i++) {
            array[i] = feeTypeDTO.getPrincipalEachTermFeeAmount()[i];
        }

        //减免期数
        feeTypeDTO.setPrincipalDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //本金减免金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);
    }

    /**
     * 减免金额
     * @param feeTypeDTO 手续费计算涉及的参数
     * @param installParameter 分期参数
     */
    private void principalBreaksAmount(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //本金 金额减免值
        BigDecimal principalAmountValue = Optional.ofNullable(installParameter.getInstallFeeCode().getPrincipalAmountValue()).orElse(BigDecimal.ZERO);


        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);

        //获取 本金金额
        BigDecimal[] eachTermPrincipalAmount = feeTypeDTO.getPrincipalEachTermFeeAmount();
        //减免值
        BigDecimal temp = new BigDecimal(principalAmountValue.toString());

        for (int i = 0; i < eachTermPrincipalAmount.length ; i++){

            if (temp.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }

            if (eachTermPrincipalAmount[i].compareTo(BigDecimal.ZERO) == 0){
                array[i] = BigDecimal.ZERO;
            }else if (eachTermPrincipalAmount[i].compareTo(temp) >= 0){
                array[i] = temp;
                temp = temp.subtract(eachTermPrincipalAmount[i]);
            }else {
                array[i] = eachTermPrincipalAmount[i];
                temp = temp.subtract(eachTermPrincipalAmount[i]);
            }
        }
        //减免期数
        feeTypeDTO.setPrincipalDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //本金减免金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);

    }

    /**
     * 减免期数及金额
     *
     */
    private void principalBreaksAmountAndTerm(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //减免期数
        Integer term = installParameter.getInstallOrder().getTerm();

        //本金减免金额
        BigDecimal principalAmountValue = Optional.ofNullable(installParameter.getInstallFeeCode().getPrincipalAmountValue()).orElse(BigDecimal.ZERO);
        //本金减免期数
        BigDecimal principalTermValue = Optional.ofNullable(installParameter.getInstallFeeCode().getPrincipalTermValue()).orElse(BigDecimal.ZERO);

        int len = principalTermValue.intValue() > installParameter.getInstallOrder().getTerm()
                ? installParameter.getInstallOrder().getTerm() : principalTermValue.intValue();


        BigDecimal[] array = new BigDecimal[term];
        Arrays.fill(array,BigDecimal.ZERO);

        //每期-本金分期费用金额
        for (int i = 0 ; i < len ; i++) {

            if (principalAmountValue.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }

            if (feeTypeDTO.getPrincipalEachTermFeeAmount()[i].compareTo(BigDecimal.ZERO) == 0){
                continue;
            }

            if (principalAmountValue.compareTo(feeTypeDTO.getPrincipalEachTermFeeAmount()[i]) <= 0){
                array[i] = principalAmountValue;
            }else{
                array[i] = feeTypeDTO.getPrincipalEachTermFeeAmount()[i];
            }
            principalAmountValue = principalAmountValue.subtract(array[i]);
        }

        //减免期数
        feeTypeDTO.setPrincipalDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());

        //本金减免金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);

    }

    /**
     * 设置本金费用金额
     */
    private void setEachPrincipalTermArray(BigDecimal termAmount, BigDecimal firstAmount,
                                           InstallParameterDTO installParameter,
                                           FeeTypeDTO feeTypeDTO) {

        //还款方式
        String paymentWay = installParameter.getInstallProInfo().getPaymentWay();

        //尾款方式 0-余数放首期、1-余数放尾期
        String balanceMethod = installParameter.getInstallProInfo().getBalanceMethod();
        //期数
        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];

        //等费等本
        if (Objects.equals(PaymentWayEnum.EQUAL_COST_EQUAL_CAPITAL.getCode(), paymentWay)) {

            Arrays.fill(array, termAmount);
            //余数放首期
            if (InstallManager.isFirstTerm(balanceMethod)){
                array[0] = firstAmount;
            }else {
                array[array.length - 1] = firstAmount;
            }
        }

        //先费后本
        if ((Objects.equals(PaymentWayEnum.FIRST_COST_LATER_COST.getCode(), paymentWay))){

            Arrays.fill(array, BigDecimal.ZERO);
            array[array.length - 1] = firstAmount;

        }

        feeTypeDTO.setPrincipalEachTermFeeAmount(array);

        logger.info("Installment cost array: arrayLength={}", array.length);
    }


    @Override
    public int getCount(String partitionKey) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installOrderSelfMapper.getCount(partitionKey0, partitionKey1);
    }

    @Override
    public List<String> queryOrderIds(String partitionKey, List<Integer> rowNumbers) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installOrderSelfMapper.queryOrderIds(partitionKey0, partitionKey1, rowNumbers);
    }

    /**
     * 根据id查询
     *
     * @param orderId 分期订单号的id
     * @return InstallOrderDTO
     **/
    @Override
    public InstallOrderDTO findOrderById(String orderId) {
        if (orderId == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.IN_OR_E);
        }
        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
        if (installOrder == null) {
            logger.error("Installment order not found: orderId={}", orderId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT, InstallRepDetailEnum.NULL, orderId);
        }
        InstallOrderDTO copy = BeanMapping.copy(installOrder, InstallOrderDTO.class);

        copy.setInitialPrincipalAmount(copy.getInstallmentAmount()
                .subtract(Optional.ofNullable(copy.getInstallmentTotalInterest()).orElse(BigDecimal.ZERO)));
        return copy;
    }

    /**
     * 根据管理账号 产品码 交易日期 参考号 交易金额查询订单
     *
     * @param accountManagementId 管理账户id
     * @param productCode 产品代码
     * @param transactionDate 交易日期
     * @param authorizationCode 交易序列号
     * @param installmentAmount 分期金额
     * @return InstallOrderDTO
     **/
    @Override
    public InstallOrderDTO orderByManageAndCodeAndDateAndAcquire(String accountManagementId, String productCode,
                                                                 LocalDate transactionDate, String authorizationCode,
                                                                 BigDecimal installmentAmount) {
        if (accountManagementId == null) {
            logger.error("Account management id cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.MA_AC_E);
        }
        if (productCode == null) {
            logger.error("Installment product code cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT,  InstallRepDetailEnum.ST_PRC_E);
        }
        if (transactionDate == null) {
            logger.error("Transaction date cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_D_E);
        }
        if (authorizationCode == null) {
            logger.error("Authorization code cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AU_C_E);
        }
        InstallOrder installOrder = installOrderSelfMapper.orderByManageAndCodeAndDateAndAcquire(accountManagementId,
                productCode, transactionDate, authorizationCode, installmentAmount);
        if (installOrder == null) {
            logger.error("Installment order not found: accountManagementId={}, productCode={}, transactionDate={}, authorizationCode={}, installmentAmount={}",
                    accountManagementId, productCode, transactionDate, authorizationCode, installmentAmount);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
        }
        return BeanMapping.copy(installOrder, InstallOrderDTO.class);
    }

    /**
     * 根据全局流水号查询订单
     *
     * @param originalGlobalFlowNumber 全局流水号
     * @return InstallOrderDTO
     **/
    @Override
    public InstallOrderDTO selectByGlobalFlowNumber(String originalGlobalFlowNumber) {
        InstallOrder installOrder = installOrderSelfMapper.selectByGlobalFlowNumber(originalGlobalFlowNumber);
        if (installOrder == null) {
            return null;
        }
        return BeanMapping.copy(installOrder, InstallOrderDTO.class);
    }

    /**
     * 根据机构号、管理账户id查询分期订单表
     *
     * @param orgNum              机构号
     * @param accountManagementId 管理账户id
     * @return List<InstallOrderDTO>
     */
    @Override
    public List<InstallOrderDTO> selectByOrgNumAndAcctManageId(String orgNum, String accountManagementId) {
        logger.debug("Query installment order by organization number and account management id");
        if (orgNum == null) {
            logger.error("Organization number cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.OR_E);
        }
        if (accountManagementId == null) {
            logger.error("Account management id cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.MA_AC_E);
        }
        List<InstallOrder> installOrders = installOrderSelfMapper.selectByOrgNumAndAcctManageId(orgNum,
                accountManagementId);
        return BeanMapping.copyList(installOrders, InstallOrderDTO.class);
    }


    /**
     * 分页查询所有分期订单列表信息
     *
     * @return PageResultDTO<InstallOrderResDTO>
     */
    @Override
    public PageResultDTO<InstallOrderDTO> findAll(Integer pageNum, Integer pageSize) {
        logger.debug("Paginated query for all installment order list information");
        Page<InstallOrder> page = PageHelper.startPage(pageNum, pageSize);
        List<InstallOrder> installOrders = installOrderSelfMapper.selectAll();
        //查询结果判断
        if (installOrders.isEmpty()) {
            logger.error("Paginated query for installment orders returned no data");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
        }
        List<InstallOrderDTO> installOrderResDtos = BeanMapping.copyList(installOrders, InstallOrderDTO.class);
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), installOrderResDtos);
    }

    /**
     * 根据
     * 机构号、管理账号、分期订单号、卡号、分期产品代码、交易日期、分期授权码、分期金额
     * 分页查询所有分期订单列表信息
     *
     * @return PageResultDTO<InstallOrderResDTO>
     */
    @Override
    public PageResultDTO<InstallOrderDTO> orderByOrgAndManageAndProductAndDateAndAuthorAndAmount(InstallOrderSearchKeyDTO installOrderSearchKeyDTO) {
        logger.debug("Paginated query for all installment order list information based on input parameters");
        Page<InstallOrder> page = PageHelper.startPage(installOrderSearchKeyDTO.getPage(),
                installOrderSearchKeyDTO.getRows());
        Map<String, Object> map = new HashMap<>(8);
        map.put("organizationNumber", installOrderSearchKeyDTO.getOrganizationNumber());
        map.put("accountManagementId", installOrderSearchKeyDTO.getAccountManagementId());
        map.put("orderId", installOrderSearchKeyDTO.getOrderId());
        map.put("cardNumber", installOrderSearchKeyDTO.getCardNumber());
        map.put("productCode", installOrderSearchKeyDTO.getProductCode());
        map.put("transactionDate", installOrderSearchKeyDTO.getTransactionDate());
        map.put("authorizationCode", installOrderSearchKeyDTO.getAuthorizationCode());
        map.put("installmentAmount", installOrderSearchKeyDTO.getInstallmentAmount());
        map.put("customerId", installOrderSearchKeyDTO.getCustomerId());

        List<InstallOrder> installOrders = Collections.emptyList();
        if (!StringUtils.isAllBlank(installOrderSearchKeyDTO.getAccountManagementId(),
                installOrderSearchKeyDTO.getOrderId(),
                installOrderSearchKeyDTO.getCardNumber(),
                installOrderSearchKeyDTO.getCustomerId())){
            installOrders = installOrderSelfMapper.selectByOptions(map);
        }

        List<InstallOrderDTO> installOrderResDtos = BeanMapping.copyList(installOrders, InstallOrderDTO.class);
        return new PageResultDTO<>(installOrderSearchKeyDTO.getPage(), installOrderSearchKeyDTO.getRows(),
                page.getTotal(), page.getPages(), installOrderResDtos);
    }

    /**
     * 初始化核心入账实体
     *
     * @param installOrder 分期订单好
     * @param createTransactionCode 入账交易码
     * @return RecordedBO
     */
    public RecordedBO getRecorded(InstallOrderDTO installOrder, String createTransactionCode, String originTransactionId,InstallProductInfoResDTO installProductInfo) {
        //获取系统当前业务日期
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrder.getOrganizationNumber());
        if (organizationInfo == null) {
            logger.error("Organization parameter does not exist: organizationId={}", installOrder.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }
        //调用账户入帐接口进行入帐（贷调交易码取分期产品贷记交易码）
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(installOrder.getAccountManagementId());
        recorded.setTxnAuthorizationCode(InstallmentConstant.AUTHORIZATION_CODE);
        //授权匹配标志
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //入账金额
        if(createTransactionCode.startsWith("6")){
            recorded.setTxnBillingAmount(installProductInfo.getTransferFee());
        }else{
            recorded.setTxnBillingAmount(installOrder.getInstallmentAmount());
        }
        //入账币种
        recorded.setTxnBillingCurrency(installOrder.getInstallmentCcy());

        if (!Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrder.getType())) {
            // 整笔分期赋值订单交易日期
            recorded.setTxnTransactionDate(installOrder.getTransactionDate());
        }

        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnCityCode("");
        recorded.setTxnCountryCode("");
        recorded.setTxnDccIndicator("");
        recorded.setTxnExchangeRate(BigDecimal.ZERO);
        recorded.setTxnFallBackIndicator("");
        recorded.setTxnFeeTableId("");
        recorded.setTxnForcePostIndicator("");
        recorded.setTxnGlobalFlowNumber("");
        recorded.setTxnIfiIndicator("");
        //分期标识
        recorded.setTxnInstallmentIndicator("");
        //分期订单号
        recorded.setTxnInstallmentOrderId("");
        recorded.setTxnInstallmentTerm("");
        recorded.setTxnInterestTableId("");
        recorded.setTxnLimitNodeId("");
        recorded.setTxnMerchantCategoryCode("");
        recorded.setTxnOpponentAccountName("");
        recorded.setTxnOpponentAccountNumber("");
        recorded.setTxnOpponentBankNumber("");
        if (StringUtils.isNotEmpty(originTransactionId)) {
            PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(originTransactionId);
            //调整交易的原交易余额信息id
            recorded.setTxnOriginalTransactionBalanceId(postedTransaction.getTransactionBalanceId());
            //调整原交易的已入账交易ID
            recorded.setTxnOriginalPostedTransactionId(originTransactionId);
            //原始交易日期
            recorded.setTxnOriginalTransactionDate(postedTransaction.getTransactionDate());
            //原交易商户名商户ID赋值分期调整的交易
            recorded.setTxnMerchantId(postedTransaction.getMerchantId());
            recorded.setTxnMerchantName(postedTransaction.getMerchantName());
        }
        //原全局业务流水号
        recorded.setTxnOriginalGlobalFlowNumber("");
        //授权额度占用金额
        recorded.setTxnOutstandingAmount(BigDecimal.ZERO);
        //父级交易账户ID
        recorded.setTxnParentTransactionAccountId("");
        //POS输入方式
        recorded.setTxnPosEntryMode("");
        //入账方式
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        recorded.setTxnPsvIndicator("");
        recorded.setTxnReferenceNumber("");
        recorded.setTxnReimbursementAttribute("");
        //是否恢复授权占用额度标志
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        //拒绝重入账标志
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        recorded.setTxnReverseFeeIndicator("");
        recorded.setTxnSecondMerchantId("");
        recorded.setTxnSecondMerchantName("");
        //清算金额
        recorded.setTxnSettlementAmount(installOrder.getInstallmentAmount());
        //清算币种
        recorded.setTxnSettlementCurrency(installOrder.getInstallmentCcy());
        recorded.setTxnStateCode("");
        //交易金额
        if(createTransactionCode.startsWith("6")){
            recorded.setTxnTransactionAmount(installProductInfo.getTransferFee());
        }else{
            recorded.setTxnTransactionAmount(installOrder.getInstallmentAmount());
        }
        //入账交易码
        recorded.setTxnTransactionCode(createTransactionCode);
        //交易币种
        recorded.setTxnTransactionCurrency(installOrder.getInstallmentCcy());
        if(createTransactionCode.startsWith("6")){
            recorded.setTxnTransactionDescription(installOrder.getTransactionDesc() + " Processing  fee");
        }else{
            recorded.setTxnTransactionDescription(installOrder.getTransactionDesc() + " Credit adjustment");
        }
        //0=本地输入1=本行外部输入2=内生交易
        recorded.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        recorded.setTxnVisaChargeFlag("");
        recorded.setTxnZipCode("");
        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnInstallmentOrderId(installOrder.getOrderId());
        //分期类型
        recorded.setInstallmentType(installOrder.getType());
        return recorded;
    }


    private InstallAccountingTransParmResDTO getInstallAccountTranByOrgNumAndTableId(String organizationNumber, String tableId) {
        InstallAccountingTransParmResDTO result = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        if (result == null) {
            logger.error("Failed to query installment accounting transaction parameters: organizationNumber={}, tableId={}", organizationNumber, tableId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }

    public TransactionCodeResDTO findParamTransactionCode(String transactionCode, String organizationNumber) {
        TransactionCodeResDTO result = transactionCodeService.findTransactionCode(organizationNumber, transactionCode);
        if (result == null) {
            logger.error("Failed to query transaction code parameters: organizationNumber={}, transactionCode={}", organizationNumber, transactionCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_TRANS_CODE_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }

    /**
     * 下一账单日
     *
     * @param nextProcessingDay 下一处理日
     * @param cycleDate         账单日
     * @return 下一账单日
     */
    private LocalDate calculateStatementDate(LocalDate nextProcessingDay, short cycleDate) {
        int month;
        int year;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month >= 13) {
            month = month - 12;
            year = year + 1;
        }
        return LocalDate.of(year, month, cycleDate);
    }
}
