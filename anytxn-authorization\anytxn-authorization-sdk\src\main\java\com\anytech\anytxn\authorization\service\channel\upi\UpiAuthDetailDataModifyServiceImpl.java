package com.anytech.anytxn.authorization.service.channel.upi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.authorization.base.domain.model.*;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.base.enums.AuthProcessingCodeEnum;
import com.anytech.anytxn.authorization.base.enums.RecordedEnum;
import com.anytech.anytxn.authorization.base.enums.TrasactionStatusEnum;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.mapper.epcc.AuthorizationLogEpccSelfMapper;
import com.anytech.anytxn.authorization.mapper.express.AuthorizationLogExpressMapper;
import com.anytech.anytxn.authorization.mapper.jcb.AuthorizationLogJcbMapper;
import com.anytech.anytxn.authorization.mapper.jcb.AuthorizationLogJcbSelfMapper;
import com.anytech.anytxn.authorization.mapper.manauthLog.ManagementAuthLogMapper;
import com.anytech.anytxn.authorization.mapper.master.AuthorizationLogMcMapper;
import com.anytech.anytxn.authorization.mapper.master.AuthorizationLogMcSelfMapper;
import com.anytech.anytxn.authorization.mapper.preauthlog.PreAuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.mapper.visa.AuthorizationLogVisaMapper;
import com.anytech.anytxn.authorization.mapper.visa.AuthorizationLogVisaSelfMapper;
import com.anytech.anytxn.authorization.base.service.auth.IAuthMatchRuleService;
import com.anytech.anytxn.authorization.base.service.auth.IAuthorizationLogService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.authorization.service.auth.CheckAuthResponseCodeServiceImpl;
import com.anytech.anytxn.authorization.service.auth.LimitRequestPrepareService;
import com.anytech.anytxn.authorization.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.service.manager.AuthCheckItemManager;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.base.utils.Field63Utils;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.PartnerOriginTransInfoDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.limit.constant.Constant;
import com.anytech.anytxn.limit.base.constant.LimitConstant;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * UPI
 * @ClassName AuthDataUpdateDetail
 * @Description 授权流水表,卡片授权信息表,未并账信息表
 * <AUTHOR>
 * @date  2018/12/17 10:35 AM
 * Version 1.0
 **/
@Service
public class UpiAuthDetailDataModifyServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(UpiAuthDetailDataModifyServiceImpl.class);
    @Autowired
    private AuthorizationLogVisaMapper authorizationLogVisaMapper;
    @Autowired
    private AuthorizationLogMcMapper authorizationLogMcMapper;
    @Autowired
    private AuthorizationLogJcbMapper authorizationLogJcbMapper;
    @Autowired
    private AuthorizationLogVisaSelfMapper authorizationLogVisaSelfMapper;
    @Autowired
    private AuthorizationLogEpccSelfMapper authorizationLogEpccSelfMapper;
    @Autowired
    private AuthorizationLogMcSelfMapper authorizationLogMcSelfMapper;
    @Autowired
    private AuthorizationLogJcbSelfMapper authorizationLogJcbSelfMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private IAuthorizationLogService authorizationLogService;
    @Autowired
    private IPreAuthorizationLogService preAuthorizationLogService;
    @Autowired
    private IOutstandingTransService outstandingTransService;
    @Autowired
    private LimitRequestPrepareService limitRequestPrepareService;
    @Autowired
    private PreAuthorizationLogSelfMapper preAuthorizationLogSelfMapper;
    @Resource
    private AuthCheckItemManager authCheckItemManager;
    @Resource
    private AuthorizationLogExpressMapper authorizationLogExpressMapper;
    @Autowired
    private IAuthMatchRuleService authMatchRuleService;
    @Resource
    private ManagementAuthLogMapper managementAuthLogMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Resource
    private PartitionKeyInitService partitionKeyInitService;

    @Autowired
    private AuthorizationLogSelfMapper authorizationLogSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    /**
     * 授权流水表更新处理
     * @param authRecordedDTO  AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    public int modifyAuthorizationLog(AuthRecordedDTO authRecordedDTO) {
        //1.如果是冲正、撤销、撤销冲正交易，还需要更新原交易的授权流水记录。
        boolean flag = AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                ||AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                ||AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode());
        if(flag){
            if (AuthTransactionSourceCodeEnum.UPI.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
                authorizationLogUpdate(authRecordedDTO);
            }
            //visa
            if (AuthTransactionSourceCodeEnum.VISA.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
                visaAuthorizationLogUpdate(authRecordedDTO);
            }
            if (AuthTransactionSourceCodeEnum.JCB.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
                jcbAuthorizationLogUpdate(authRecordedDTO);
            }
            if (AuthTransactionSourceCodeEnum.MASTERCARD.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
                mcAuthorizationLogUpdate(authRecordedDTO);
            }
            if (Objects.equals(AuthTransactionSourceCodeEnum.EXPRESS.getCode(),authRecordedDTO.getAuthTransactionSourceCode())){
                expressAuthorizationLogUpdate(authRecordedDTO);
            }
        }
        //2.置本笔交易状态STATUS = 1,  置冲正类型TYPE = 接口中的authReversalType,  按照赋值规则Rule-3写本交易的授权流水表
        return addAuthorizationLog(authRecordedDTO);
    }

    @BatchSharedAnnotation
    private void expressAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogExpress authorizationLog;
        AuthorizationLogExpress orignalAuthorizationLog ;
        //本交易为撤销交易(包含部分撤销)(authTransactionTypeCode为2)
        String authTransactionTypeCode = authRecordedDTO.getAuthTransactionTypeCode();
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getExpressAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogExpress(BeanMapping.copy(authorizationLog, AuthorizationLogExpressDTO.class), true);
                } else {
                    authorizationLogExpressMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.info("Express revocation operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        //本交易为冲正交易(包含部分冲正)(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getExpressAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogExpress(BeanMapping.copy(authorizationLog, AuthorizationLogExpressDTO.class), true);
                } else {
                    authorizationLogExpressMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.info("Express reversal operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getExpressAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogExpress(BeanMapping.copy(authorizationLog, AuthorizationLogExpressDTO.class), true);
                } else {
                    authorizationLogExpressMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.info("Express revocation reversal operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }

            orignalAuthorizationLog = getExpressAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (orignalAuthorizationLog != null) {
                orignalAuthorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                orignalAuthorizationLog.setReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                orignalAuthorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogExpress(BeanMapping.copy(orignalAuthorizationLog, AuthorizationLogExpressDTO.class), true);
                } else {
                    authorizationLogExpressMapper.updateByPrimaryKeySelective(orignalAuthorizationLog);
                }
            } else {
                logger.error("Express revocation reversal operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
    }


    /**
     * 主要处理预授权完成情况(授权流水表)
     * @param authRecordedDTO AuthRecordedDTO
     */
    @BatchSharedAnnotation
    public int modifyAuthorizationLogWithPreFinish(AuthRecordedDTO authRecordedDTO
            ,PreAuthorizationLogDTO preAuthorizationLogDTO) {
        //1.预授权-授权流水表更新
        preFinishauthorizationLogUpdate(authRecordedDTO,preAuthorizationLogDTO);
        //2.置本笔交易状态STATUS = 1,  置冲正类型TYPE = 接口中的authReversalType,  按照赋值规则Rule-3写本交易的授权流水表
        return addAuthorizationLog(authRecordedDTO);
    }

    @BatchSharedAnnotation
    public int outStandingTransModify(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        AuthorisationProcessingResDTO authorProcessInfo = authorizationCheckProcessingPayload.getAuthorProcessInfo();
        AccountManagementInfoDTO accountManagementInfoDTO = authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
        AuthTransTypeEnum authTransTypeEnum = AuthTransTypeEnum.getEnum(authRecordedDTO.getAuthTransactionTypeCode());
        Objects.requireNonNull(authTransTypeEnum,"AuthTransTypeEnum不能为空");
        int res ;
        switch (authTransTypeEnum) {
            case NORMAL_TRANS:
                res = transactionCode1(authorizationCheckProcessingPayload,accountManagementInfoDTO, orgInfo, authorProcessInfo);
                break;
            case REVOCATION_TRANS:
                res = transactionForRevocation(authorizationCheckProcessingPayload, accountManagementInfoDTO, orgInfo, authorProcessInfo);
                break;
            case REVERSAL_TRANS:
                res = transactionCodeReversal(authorizationCheckProcessingPayload,accountManagementInfoDTO, authRecordedDTO, orgInfo, authorProcessInfo);
                break;
            case REVOCATION_REVERSAL_TRANS:
                res = transactionCode4(authorizationCheckProcessingPayload, accountManagementInfoDTO, authRecordedDTO, orgInfo, authorProcessInfo) ;
            break;
            case REFUNDS_TRANS:
                //兼容具有原交易的退货
                if(StringUtils.isNotEmpty(authRecordedDTO.getAuthOriginalGlobalFlowNumber()) && null != authRecordedDTO.getAuthOriginalGlobalFlowNumber()){
                    res = transactionForRefunds(authorizationCheckProcessingPayload,accountManagementInfoDTO,authRecordedDTO,orgInfo,authorProcessInfo);
                }else {
                    res = transactionCode1(authorizationCheckProcessingPayload,accountManagementInfoDTO, orgInfo, authorProcessInfo);
                }
                break;
            default:
                logger.error("AuthTransactionTypeCode not in (1,2,3,4), data exception");
                return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        if(res != AuthConstans.ONE){
            logger.error("Outstanding transaction update error, trans: {}, {}", authTransTypeEnum.getCode(), authTransTypeEnum.getDescription());
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 撤销处理逻辑更新
     * 1.获取原交易的额度单元
     * 2.添加新的新的记录
     */
    @BatchSharedAnnotation
    private int transactionForRevocation(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AccountManagementInfoDTO accountManagementInfo,
                                    OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        logger.info("Calling outstandingTransService.getOutstandingTransactionByGlobalFlowNumber for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthOriginalGlobalFlowNumber(), authRecordedDTO.getOrganizationNumber());
        logger.info("outstandingTransService.getOutstandingTransactionByGlobalFlowNumber completed for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (outstandingTransactionDTO == null){
            logger.error("Outstanding transaction not found for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_NOT_EXISTS);
        }
        //针对调整交易的内生撤销交易修改，原交易的交易状态修改为CANCEL
        boolean adjustmentCancelByUsFlag = StringUtils.equals(authRecordedDTO.getAuthProcessingCode(),AuthProcessingCodeEnum.ADJUSTMENT_CODE.getCode()) &&
                StringUtils.equals(authRecordedDTO.getAuthReversalType(),ReversalTypeEnum.REVOCATION_TRANS.getCode());
        if(adjustmentCancelByUsFlag){
            outstandingTransactionDTO.setTrasactionStatus(TrasactionStatusEnum.CANCEL.getCode());
            logger.info("Calling outstandingTransService.update for adjustment cancel transaction");
            outstandingTransService.update(outstandingTransactionDTO);
            logger.info("outstandingTransService.update completed for adjustment cancel transaction");
        }

        authRecordedDTO.setLimitUnitList(outstandingTransactionDTO == null
                ? null:JSONObject.parseArray(outstandingTransactionDTO.getLimitUnitJson(), CalculateLimitUnitDTO.class));

        String debitCreditIndcator = outstandingTransactionDTO.getDebitCreditIndcator();
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
        //原交易的借贷类型
        if(StringUtils.isNotBlank(debitCreditIndcator)){
            outstandingTransactionDTO.setDebitCreditIndcator(debitCreditIndcator);
        }
        return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
    }

    /**
     * DCI
     * 撤销冲正逻辑更新
     * 1.找到原交易
     * 2.删除撤销原交易
     */
    @BatchSharedAnnotation
    private int transactionForRevocationReversal(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AccountManagementInfoDTO accountManagementInfo, AuthRecordedDTO authRecordedDTO,
                                 OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        logger.info("Calling outstandingTransService.getOutstandingTransactionByGlobalFlowNumber for globalFlowNumber: {}", authRecordedDTO.getAuthGlobalFlowNumber());
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthGlobalFlowNumber(),
                authRecordedDTO.getOrganizationNumber());
        logger.info("outstandingTransService.getOutstandingTransactionByGlobalFlowNumber completed for globalFlowNumber: {}", authRecordedDTO.getAuthGlobalFlowNumber());
        if (outstandingTransactionDTO == null){
            logger.error("Outstanding transaction not found for globalFlowNumber: {}", authRecordedDTO.getAuthGlobalFlowNumber());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_NOT_EXISTS);
        }
        if (CustAccountBO.isBatch() && outstandingTransactionDTO != null) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkOutstandingTransaction(outstandingTransactionDTO);
        }
        authRecordedDTO.setLimitUnitList(outstandingTransactionDTO == null
                ? null : JSONObject.parseArray(outstandingTransactionDTO.getLimitUnitJson(), CalculateLimitUnitDTO.class));

        //删除原交易
        if (outstandingTransactionDTO != null && PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())) {
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().delOutstandingTransaction(outstandingTransactionDTO.getOutstandingTransactionId());
                return 1;
            } else {
                logger.info("Calling outstandingTransService.delOutstandingTransactionById");
                int result = outstandingTransService.delOutstandingTransactionById(outstandingTransactionDTO);
                logger.info("outstandingTransService.delOutstandingTransactionById completed, result: {}", result);
                return result;
            }
        }
        logger.info("Calling outstandingTransService.delOutstandingTransactionById");
        int result = outstandingTransService.delOutstandingTransactionById(outstandingTransactionDTO);
        logger.info("outstandingTransService.delOutstandingTransactionById completed, result: {}", result);
        return result;
    }

    /**
     *  普通交易的冲正逻辑处理
     *
     *  1.如果是部分冲正则 按照部分退货逻辑处理
     *  2.如果是完全冲正,则按照撤销逻辑处理
     */
    private int transactionCodeReversal(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                 AccountManagementInfoDTO accountManagementInfoDTO,
                                 AuthRecordedDTO authRecordedDTO, OrganizationInfoResDTO orgInfo,
                                 AuthorisationProcessingResDTO authorProcessInfo) {
        logger.info("Calling outstandingTransService.getOutstandingTransactionByGlobalFlowNumber for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthOriginalGlobalFlowNumber(),
                authRecordedDTO.getOrganizationNumber());
        logger.info("outstandingTransService.getOutstandingTransactionByGlobalFlowNumber completed for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (outstandingTransactionDTO == null){
            logger.error("Outstanding transaction not found for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_NOT_EXISTS);
        }
        //没有原交易的退货冲正处理
        if(null == outstandingTransactionDTO.getLimitUnitJson() || !StringUtils.isNotEmpty(outstandingTransactionDTO.getLimitUnitJson())){
            int partitionKeyGenerator = partitionKeyInitService.partitionKeyGenerator(
                    authorizationCheckProcessingPayload.getCardAuthorizationDTO(), null);
            outstandingTransactionDTO.setPartitionKey((long) partitionKeyGenerator);
            return commonProcessOutStanding(authRecordedDTO,outstandingTransactionDTO);
        }
        ImmutablePair<List<CalculateLimitUnitDTO>, List<CalculateLimitUnitDTO>> listListImmutablePair = limitReFunds(outstandingTransactionDTO.getLimitUnitJson(), authRecordedDTO.getAuthCardholderBillingAmount());
        if (PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())){
            //未入账的处理
            long count = listListImmutablePair.getRight()
                    .stream()
                    .filter(t -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(t.getLimitUnitCode()) && t.getOverdrawAmount().compareTo(BigDecimal.ZERO) > 0)
                    .count();

            //用户额度恢复
            authRecordedDTO.setLimitUnitList(listListImmutablePair.getLeft());

            //全部冲正
            if (count == 0){
                return commonProcessOutStanding(authRecordedDTO,outstandingTransactionDTO);
            }else {
                //部分冲正
                outstandingTransactionDTO.setAmountReturned(Optional.ofNullable(outstandingTransactionDTO.getAmountReturned()).orElse(BigDecimal.ZERO)
                        .add(authRecordedDTO.getAuthTransactionAmount()));

                //原始交易的额度占用以及金额修正
                outstandingTransactionDTO.setOutstandingAmount(outstandingTransactionDTO.getOutstandingAmount().subtract(authRecordedDTO.getAuthCardholderBillingAmount()));
                outstandingTransactionDTO.setBillingAmount(outstandingTransactionDTO.getBillingAmount().subtract(authRecordedDTO.getAuthCardholderBillingAmount()));

                outstandingTransactionDTO.setLimitUnitJson(JSON.toJSONString(listListImmutablePair.getRight()));
                logger.info("Calling outstandingTransService.update for partial reversal");
                int result = outstandingTransService.update(outstandingTransactionDTO);
                logger.info("outstandingTransService.update completed for partial reversal, result: {}", result);
                return result;
            }
        }
        //已入账处理
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
        return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
     }

    /**
     * CUP
     * 退货交易的未并账交易处理
     * 1.额度分配
     * 2.将分配后剩余的额度信息更新回去
     * 3.已退货金额的累加处理
     */
    private int transactionCodeRefundsCup(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                       AccountManagementInfoDTO accountManagementInfoDTO, AuthRecordedDTO authRecordedDTO,
                                       OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthOriginalGlobalFlowNumber(),
                authRecordedDTO.getOrganizationNumber());


        if (CustAccountBO.isBatch() && outstandingTransactionDTO != null) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkOutstandingTransaction(outstandingTransactionDTO);
        }
        if(outstandingTransactionDTO != null){
            //退货交易,由于存在部分退货，所以需要一直更新原正向交易的未并账交易表的额度占用信息
            JSONArray array = JSONObject.parseArray(outstandingTransactionDTO.getLimitUnitJson());
            JSONObject json = new JSONObject();
            BigDecimal residualAmount =authRecordedDTO.getAuthCardholderBillingAmount();
            json.put("residualAmount",residualAmount);
            array.stream()
                    .forEach(jsonobject->{
                        if(json.getBigDecimal("residualAmount").compareTo(((JSONObject)jsonobject).getBigDecimal("overdrawAmount"))<=0){
                            ((JSONObject) jsonobject).put("overdrawAmount",json.getBigDecimal("residualAmount"));
                            json.put("residualAmount",BigDecimal.ZERO);
                        }else{
                            BigDecimal residual = json.getBigDecimal("residualAmount").subtract(((JSONObject)jsonobject).getBigDecimal("overdrawAmount"));
                            json.put("residualAmount",residual);
                        }
                    });
            authRecordedDTO.setLimitUnitList(JSONObject.parseArray(JSON.toJSONString(array.stream().filter(jsonobejct->((JSONObject)jsonobejct).getBigDecimal("overdrawAmount").compareTo(BigDecimal.ZERO)>0).collect(Collectors.toList())), CalculateLimitUnitDTO.class));
        }
        outstandingTransactionDTO.setAmountReturned(outstandingTransactionDTO.getAmountReturned().add(authRecordedDTO.getAuthCardholderBillingAmount()));
        if (outstandingTransactionDTO != null && PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())) {
            //退货原入账处理
            List<CalculateLimitUnitDTO> limitUnitList = authRecordedDTO.getLimitUnitList();
            if (!CollectionUtils.isEmpty(limitUnitList)){
                outstandingTransactionDTO.setLimitUnitJson(JSONArray.toJSONString(limitUnitList));
            }
            outstandingTransactionDTO.setOutstandingAmount(outstandingTransactionDTO.getOutstandingAmount().subtract(authRecordedDTO.getAuthCardholderBillingAmount()));
            return outstandingTransService.update(outstandingTransactionDTO);
        }else{
            //原交易更新
            outstandingTransService.update(outstandingTransactionDTO);
            outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);

            return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
        }
    }

    /**
     * DCI
     * 退货交易逻辑更新
     * 1.全部退货，添加一条新的记录
     * 2.部分退货，更新原交易，添加一条新的记录
     */
    private int transactionForRefunds(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                       AccountManagementInfoDTO accountManagementInfoDTO, AuthRecordedDTO authRecordedDTO,
                                       OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        logger.info("Calling outstandingTransService.getOutstandingTransactionByGlobalFlowNumber for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthOriginalGlobalFlowNumber(),
                authRecordedDTO.getOrganizationNumber());
        logger.info("outstandingTransService.getOutstandingTransactionByGlobalFlowNumber completed for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (outstandingTransactionDTO == null || StringUtils.isEmpty(outstandingTransactionDTO.getLimitUnitJson())){
            logger.error("Outstanding transaction not found or limitUnitJson is empty for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_NOT_EXISTS);
        }
        ImmutablePair<List<CalculateLimitUnitDTO>, List<CalculateLimitUnitDTO>> listListImmutablePair = limitReFunds(outstandingTransactionDTO.getLimitUnitJson(), authRecordedDTO.getAuthCardholderBillingAmount());
        if (PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())) {
            //未入账的处理
            long count = listListImmutablePair.getRight()
                    .stream()
                    .filter(t -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(t.getLimitUnitCode()) && t.getOverdrawAmount().compareTo(BigDecimal.ZERO) > 0)
                    .count();

            //用户额度恢复
            authRecordedDTO.setLimitUnitList(listListImmutablePair.getLeft());
            //全部退货
            if (count == 0){
//                String debitCreditIndcator = outstandingTransactionDTO.getDebitCreditIndcator();
//                outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
//                //原交易的借贷类型
//                if(StringUtils.isNotBlank(debitCreditIndcator)){
//                    outstandingTransactionDTO.setDebitCreditIndcator(debitCreditIndcator);
//                }
//                return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);

                outstandingTransactionDTO.setAmountReturned(Optional.ofNullable(outstandingTransactionDTO.getAmountReturned()).orElse(BigDecimal.ZERO)
                        .add(authRecordedDTO.getAuthTransactionAmount()));
                outstandingTransactionDTO.setTrasactionStatus(TrasactionStatusEnum.REFUND.getCode());
                authorizationCheckProcessingPayload.setOutstandingTransactionDTO(outstandingTransactionDTO);
                logger.info("Calling outstandingTransService.update for full refund");
                int result = outstandingTransService.update(outstandingTransactionDTO);
                logger.info("outstandingTransService.update completed for full refund, result: {}", result);
                return result;
            }else {
                //部分退货，更新退货的金额
                outstandingTransactionDTO.setAmountReturned(Optional.ofNullable(outstandingTransactionDTO.getAmountReturned()).orElse(BigDecimal.ZERO)
                        .add(authRecordedDTO.getAuthTransactionAmount()));
                outstandingTransactionDTO.setOutstandingAmount(outstandingTransactionDTO.getOutstandingAmount().subtract(authRecordedDTO.getAuthCardholderBillingAmount()));
                //额度不使用计算过后的额度单元，使用原交易的额度
                //outstandingTransactionDTO.setLimitUnitJson(JSON.toJSONString(listListImmutablePair.getRight()));

                //更新原交易
                logger.info("Calling outstandingTransService.update for partial refund original transaction");
                outstandingTransService.update(outstandingTransactionDTO);
                logger.info("outstandingTransService.update completed for partial refund original transaction");
                //添加新的记录
                authRecordedDTO.setLimitUnitList(listListImmutablePair.getLeft());
                // add by zcli 有原交易的部分退货，交易管控单元ID  使用原交易的
                if (StringUtils.isEmpty(authRecordedDTO.getTransCtrlUnitId())) {
                    authRecordedDTO.setTransCtrlUnitId(outstandingTransactionDTO.getTransCtrlUnitId());
                }
                String debitCreditIndcator = outstandingTransactionDTO.getDebitCreditIndcator();
                outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
                //原交易的借贷类型
                if(StringUtils.isNotBlank(debitCreditIndcator)){
                    outstandingTransactionDTO.setDebitCreditIndcator(debitCreditIndcator);
                }
                return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
            }
        }
        authRecordedDTO.setLimitUnitList(listListImmutablePair.getLeft());
        String debitCreditIndcator = outstandingTransactionDTO.getDebitCreditIndcator();
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
        //原交易的借贷类型
        if(StringUtils.isNotBlank(debitCreditIndcator)){
            outstandingTransactionDTO.setDebitCreditIndcator(debitCreditIndcator);
        }
        return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
    }


    /**
     *
     * 假设消费的额度分配为 [{"limitUnitCode":"1","overdrawAmount":"10"},{"limitUnitCode":"2","overdrawAmount":"20"}]
     *
     * 部分冲正的金额为 13
     *
     * 则 left 返回的是本次需要撤销的金额分配 [{"limitUnitCode":"1","overdrawAmount":"10"},{"limitUnitCode":"2","overdrawAmount":"3"}]
     * 则 right 返回的是本次撤销后剩余的金额分配 [{"limitUnitCode":"1","overdrawAmount":"0"},{"limitUnitCode":"2","overdrawAmount":"17"}]
     *
     */
    private ImmutablePair<List<CalculateLimitUnitDTO>, List<CalculateLimitUnitDTO>> limitReFunds(String limitUnitJson, BigDecimal residualAmount){

        residualAmount = Optional.ofNullable(residualAmount).orElse(BigDecimal.ZERO);

        //当前交易分配的额度信息
        List<CalculateLimitUnitDTO> currentLimit = JSONObject.parseArray(limitUnitJson, CalculateLimitUnitDTO.class);
        //分配后剩余的额度信息
        List<CalculateLimitUnitDTO> arrayResidual = JSONObject.parseArray(limitUnitJson, CalculateLimitUnitDTO.class);


        for (int i = 0,lent = currentLimit.size(); i < lent; i++) {
            CalculateLimitUnitDTO calculateLimitUnitDTO = currentLimit.get(i);
            if (LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(calculateLimitUnitDTO.getLimitUnitCode())) {
                AuthThreadLocalManager.getAuthRecordeddtoThreadLocal().setPartnerMaxRecoverAmount(new BigDecimal(calculateLimitUnitDTO.getOverdrawAmount().toString()));
                calculateLimitUnitDTO.setOverdrawAmount(residualAmount.min(calculateLimitUnitDTO.getOverdrawAmount()));
                CalculateLimitUnitDTO residualUnitInfo = arrayResidual.get(i);
                residualUnitInfo.setOverdrawAmount(residualUnitInfo.getOverdrawAmount().subtract(residualAmount).max(BigDecimal.ZERO));
                continue;
            }

            if(residualAmount.compareTo(calculateLimitUnitDTO.getOverdrawAmount()) <= 0){
                calculateLimitUnitDTO.setOverdrawAmount(residualAmount);
                residualAmount = BigDecimal.ZERO;
            }else{
                residualAmount = residualAmount.subtract(calculateLimitUnitDTO.getOverdrawAmount());
            }

            CalculateLimitUnitDTO residualUnitInfo = arrayResidual.get(i);
            BigDecimal subtract = residualUnitInfo.getOverdrawAmount().subtract(calculateLimitUnitDTO.getOverdrawAmount());
            residualUnitInfo.setOverdrawAmount(subtract.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : subtract);
        }

        logger.info("Amount after reversal transaction allocation");

        return ImmutablePair.of(currentLimit,arrayResidual);
    }


    /**
     * 退货交易的未并账交易处理
     * 1.额度分配
     * 2.将分配后剩余的额度信息更新回去
     * 3.已退货金额的累加处理
     */
    private int transactionCodeRefunds(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                      AccountManagementInfoDTO accountManagementInfoDTO, AuthRecordedDTO authRecordedDTO,
                                      OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {

        logger.info("Calling outstandingTransService.getOutstandingTransactionByGlobalFlowNumber for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthOriginalGlobalFlowNumber(),
                authRecordedDTO.getOrganizationNumber());
        logger.info("outstandingTransService.getOutstandingTransactionByGlobalFlowNumber completed for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());

        if (outstandingTransactionDTO == null){
            logger.error("Outstanding transaction not found for globalFlowNumber: {}", authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_NOT_EXISTS);
        }

        ImmutablePair<List<CalculateLimitUnitDTO>, List<CalculateLimitUnitDTO>> listListImmutablePair = limitReFunds(outstandingTransactionDTO.getLimitUnitJson(), authRecordedDTO.getAuthCardholderBillingAmount());

        if (PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())){

            //未入账的处理
            long count = listListImmutablePair.getRight()
                    .stream()
                    .filter(t -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(t.getLimitUnitCode()) && t.getOverdrawAmount().compareTo(BigDecimal.ZERO) > 0)
                    .count();

            //用户额度恢复
            authRecordedDTO.setLimitUnitList(listListImmutablePair.getLeft());

            //全部退货
            if (count == 0){
                //设置分区号
                int partitionKeyGenerator = partitionKeyInitService.partitionKeyGenerator(
                        authorizationCheckProcessingPayload.getCardAuthorizationDTO(), null);
                outstandingTransactionDTO.setPartitionKey((long) partitionKeyGenerator);
                return commonProcessOutStanding(authRecordedDTO,outstandingTransactionDTO);
            }else {
                //部分退货
                outstandingTransactionDTO.setAmountReturned(Optional.ofNullable(outstandingTransactionDTO.getAmountReturned()).orElse(BigDecimal.ZERO)
                        .add(authRecordedDTO.getAuthTransactionAmount()));

                //原始交易的额度占用以及金额修正
                outstandingTransactionDTO.setOutstandingAmount(outstandingTransactionDTO.getOutstandingAmount().subtract(authRecordedDTO.getAuthCardholderBillingAmount()));
                outstandingTransactionDTO.setLimitUnitJson(JSON.toJSONString(listListImmutablePair.getRight()));
                logger.info("Calling outstandingTransService.update for partial refund in transactionCodeRefunds");
                int result = outstandingTransService.update(outstandingTransactionDTO);
                logger.info("outstandingTransService.update completed for partial refund in transactionCodeRefunds, result: {}", result);
                return result;
            }
        }

        //原交易更新
        authRecordedDTO.setLimitUnitList(listListImmutablePair.getLeft());
        String debitCreditIndcator = outstandingTransactionDTO.getDebitCreditIndcator();
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
        //原交易的借贷类型
        if(StringUtils.isNotBlank(debitCreditIndcator)){
            outstandingTransactionDTO.setDebitCreditIndcator(debitCreditIndcator);
        }
        return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
    }

    /**
     * 如果authTransactionTypeCode为1（授权，包含消费、取现和还款交易）
     */
    @BatchSharedAnnotation
    private int transactionCode1(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AccountManagementInfoDTO accountManagementInfo,
                                 OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        return addOutStandingTransaction(authorizationCheckProcessingPayload,accountManagementInfo, authRecordedDTO, orgInfo, authorProcessInfo);
    }

    /**
     * 如果authTransactionTypeCode为2（现阶段指的是消费取现的撤销)
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int transactionCode2(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                 AccountManagementInfoDTO accountManagementInfo, AuthRecordedDTO authRecordedDTO,
                                 OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {

        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                authRecordedDTO.getAuthOriginalGlobalFlowNumber(),
                authRecordedDTO.getOrganizationNumber());


        if (CustAccountBO.isBatch() && outstandingTransactionDTO != null) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkOutstandingTransaction(outstandingTransactionDTO);
        }

        authRecordedDTO.setLimitUnitList(outstandingTransactionDTO == null
                ? null:JSONObject.parseArray(outstandingTransactionDTO.getLimitUnitJson(), CalculateLimitUnitDTO.class));

        if (outstandingTransactionDTO != null && PostFlagEnum.UNPROCESSED.getCode()
                .equals(outstandingTransactionDTO.getPostFlag())) {
            //撤销处理
            return commonProcessOutStanding(authRecordedDTO,outstandingTransactionDTO);
        }
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);

        return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
    }

    /**
     * 如果authTransactionTypeCode为3（现阶段指的是消费取现的冲正)
     * @param authRecordedDTO AuthRecordedDTO
     */
    @BatchSharedAnnotation
    private int transactionCode3(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AccountManagementInfoDTO accountManagementInfo, AuthRecordedDTO authRecordedDTO,
                                 OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService
                .getOutstandingTransactionByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber(),authRecordedDTO.getOrganizationNumber());
        if (CustAccountBO.isBatch() && outstandingTransactionDTO != null) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkOutstandingTransaction(outstandingTransactionDTO);
        }
//        authRecordedDTO.setLimitUnitCode(outstandingTransactionDTO==null?null:outstandingTransactionDTO.getLimitUnitCode());
//        authRecordedDTO.setLimitUnitVersion(outstandingTransactionDTO==null?null:outstandingTransactionDTO.getLimitUnitVersion());
        authRecordedDTO.setLimitUnitList(outstandingTransactionDTO==null?null:JSONObject.parseArray(outstandingTransactionDTO.getLimitUnitJson(), CalculateLimitUnitDTO.class));
        if (outstandingTransactionDTO != null && PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())) {
            //冲正处理
            return commonProcessOutStanding(authRecordedDTO,outstandingTransactionDTO);
        }
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);
        return modifyOutStandingTransaction6(authorizationCheckProcessingPayload,outstandingTransactionDTO,authRecordedDTO);
    }

    /**
     * 针对撤销和冲正的共同处理
     */
    @BatchSharedAnnotation
    private int commonProcessOutStanding(AuthRecordedDTO authRecordedDTO,OutstandingTransactionDTO outstandingTransactionDTO){
        //部分撤销
        boolean partFlag = authRecordedDTO.getAuthReplaceAmountActualMc()!= null
                || authRecordedDTO.getAuthReplaceAmountActualVisa()!= null
                || authRecordedDTO.getAuthReplaceAmountActualJcb()!= null;
        if(partFlag){
            BigDecimal transactionAmount = outstandingTransactionDTO.getTransactionAmount();
            BigDecimal outstandingAmount = outstandingTransactionDTO.getOutstandingAmount();
            BigDecimal billingAmount = outstandingTransactionDTO.getBillingAmount();
            //mc
            if(authRecordedDTO.getAuthReplaceAmountActualMc() != null){
                transactionAmount = authRecordedDTO.getAuthReplaceAmountActualMc();
                outstandingAmount = authRecordedDTO.getAuthReplaceAmountActualMc();
                billingAmount = authRecordedDTO.getAuthReplaceAmountActualMc();
            }
            //visa
            if(authRecordedDTO.getAuthReplaceAmountActualVisa() != null){
                transactionAmount = authRecordedDTO.getAuthReplaceAmountActualVisa();
                outstandingAmount = authRecordedDTO.getAuthReplaceAmountActualVisa();
                billingAmount = authRecordedDTO.getAuthReplaceAmountActualVisa();
            }
            //jcb
            if(authRecordedDTO.getAuthReplaceAmountActualJcb() != null){
                transactionAmount = authRecordedDTO.getAuthReplaceAmountActualJcb();
                outstandingAmount = authRecordedDTO.getAuthReplaceAmountActualJcb();
                billingAmount = authRecordedDTO.getAuthReplaceAmountActualJcb();
            }
            //更新
            outstandingTransactionDTO.setTransactionAmount(transactionAmount);
            outstandingTransactionDTO.setOutstandingAmount(outstandingAmount);
            outstandingTransactionDTO.setBillingAmount(billingAmount);
            outstandingTransactionDTO.setAmountReturned(outstandingTransactionDTO.getAmountReturned().add(authRecordedDTO.getAuthTransactionAmount()));
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateOutstandingTransaction(outstandingTransactionDTO, true);
                return 1;
            } else {
                return outstandingTransService.update(outstandingTransactionDTO);
            }
        }
        //全额撤销去除原交易
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().delOutstandingTransaction(outstandingTransactionDTO.getOutstandingTransactionId());
            return 1;
        } else {
            //删除原交易, 然后再备份到历史记录表中
            return outstandingTransService.delOutstandingTransactionById(outstandingTransactionDTO);
        }
    }

    /**
     * 如果authTransactionTypeCode为4（现阶段指的是消费取现的撤销冲正）
     * @param authRecordedDTO AuthRecordedDTO
     */
    @BatchSharedAnnotation
    private int transactionCode4(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AccountManagementInfoDTO accountManagementInfo, AuthRecordedDTO authRecordedDTO,
                                 OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService
                .getOutstandingTransactionByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber(),authRecordedDTO.getOrganizationNumber());
        if (CustAccountBO.isBatch() && outstandingTransactionDTO != null) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkOutstandingTransaction(outstandingTransactionDTO);
        }

        authRecordedDTO.setLimitUnitList(outstandingTransactionDTO==null
                ? null  :JSONObject.parseArray(outstandingTransactionDTO.getLimitUnitJson(), CalculateLimitUnitDTO.class));

        if (outstandingTransactionDTO != null && PostFlagEnum.UNPROCESSED.getCode().equals(outstandingTransactionDTO.getPostFlag())) {
            //设置分区号
            int partitionKeyGenerator = partitionKeyInitService.partitionKeyGenerator(
                    authorizationCheckProcessingPayload.getCardAuthorizationDTO(), null);
            outstandingTransactionDTO.setPartitionKey((long) partitionKeyGenerator);
            //去除原交易
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().delOutstandingTransaction(outstandingTransactionDTO.getOutstandingTransactionId());
                return 1;
            } else {
                return outstandingTransService.delOutstandingTransactionById(outstandingTransactionDTO);
            }
        }
        outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload,authRecordedDTO,orgInfo,authorProcessInfo);

        outstandingTransactionDTO.setPostingTransactionCode(authRecordedDTO.getPostingTransactionCode());
        outstandingTransactionDTO.setPostingTransactionCodeRev(authRecordedDTO.getPostingTransactionCodeRev());
        outstandingTransactionDTO.setDebitCreditIndcator(DebitCreditIndcatorEnum.DEBIT.getCode());
        //赋值入账方式
        if(authRecordedDTO.getPostingTransactionCode() == null
                || String.valueOf(AuthConstans.ZERO).equals(authRecordedDTO.getPostingTransactionCodeRev())){
            outstandingTransactionDTO.setPostIndicator(RecordedEnum.BATCH.getCode());
        }else{
            //对于需要实时入账 状态设置为 已处理
            outstandingTransactionDTO.setPostFlag(PostFlagEnum.PROCESSED.getCode());
            outstandingTransactionDTO.setPostIndicator(RecordedEnum.REAL_TIME.getCode());
            outstandingTransactionDTO.setTrasactionStatus(TrasactionStatusEnum.MATCHED.getCode());
        }
        authorizationCheckProcessingPayload.setOutstandingTransactionDTO(outstandingTransactionDTO);
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateOutstandingTransaction(outstandingTransactionDTO, false);
            return 1;
        } else {
            return outstandingTransService.insert(outstandingTransactionDTO);
        }
    }

    /**
     * 未并账表信息的insert setTrasactionStatus=0
     * 普通交易（消费,取现,还款）
     * @param accountManagementInfo {@link AccountManagementInfoDTO}
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @param orgInfo {@link OrganizationInfoResDTO}
     */
    @BatchSharedAnnotation
    private int addOutStandingTransaction(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AccountManagementInfoDTO accountManagementInfo, AuthRecordedDTO authRecordedDTO, OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo) {
        //未并账表的处理
        OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransactionSet(authorizationCheckProcessingPayload
                ,authRecordedDTO,orgInfo,authorProcessInfo);
        //还款类交易设为贷记标示
        if(Objects.equals(authRecordedDTO.getAuthTransactionTypeTopCode(),"P")){
            outstandingTransactionDTO.setDebitCreditIndcator(DebitCreditIndcatorEnum.CREDIT.getCode());
        }else{
            outstandingTransactionDTO.setDebitCreditIndcator(DebitCreditIndcatorEnum.DEBIT.getCode());
        }
        if(StringUtils.isBlank(authRecordedDTO.getPostingTransactionCode())){
            outstandingTransactionDTO.setPostingTransactionCode(String.valueOf(AuthConstans.ZERO));
        }else{
            outstandingTransactionDTO.setPostingTransactionCode(authRecordedDTO.getPostingTransactionCode());
        }
        if(StringUtils.isBlank(authRecordedDTO.getPostingTransactionCodeRev())){
            outstandingTransactionDTO.setPostingTransactionCodeRev(String.valueOf(AuthConstans.ZERO));
        }else{
            // 对于要实时入账状态变为 已处理
            outstandingTransactionDTO.setPostFlag(PostFlagEnum.PROCESSED.getCode());
            outstandingTransactionDTO.setPostingTransactionCodeRev(authRecordedDTO.getPostingTransactionCodeRev());
            outstandingTransactionDTO.setTrasactionStatus(TrasactionStatusEnum.MATCHED.getCode());
        }
        if(StringUtils.isNotBlank(authRecordedDTO.getPostingTransactionCode())){
            outstandingTransactionDTO.setPostIndicator(RecordedEnum.REAL_TIME.getCode());
        }else{
            outstandingTransactionDTO.setPostIndicator(RecordedEnum.BATCH.getCode());
        }
        authorizationCheckProcessingPayload.setOutstandingTransactionDTO(outstandingTransactionDTO);
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateOutstandingTransaction(outstandingTransactionDTO, false);
            return 1;
        } else {
            return outstandingTransService.insert(outstandingTransactionDTO);
        }
    }

    /**
     * 未并账信息表的update setTrasactionStatus=2
     * @param transactionDTO  {@link OutstandingTransactionDTO}
     */
    @BatchSharedAnnotation
    private int modifyOutStandingTransaction6(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, OutstandingTransactionDTO transactionDTO, AuthRecordedDTO authRecordedDTO) {
        //如果transactionDTO的原交易是借记类型则当前阶段的入账变成贷记
        if(DebitCreditIndcatorEnum.DEBIT.getCode().equals(transactionDTO.getDebitCreditIndcator())){
            transactionDTO.setDebitCreditIndcator(DebitCreditIndcatorEnum.CREDIT.getCode());
        }else{
            transactionDTO.setDebitCreditIndcator(DebitCreditIndcatorEnum.DEBIT.getCode());
        }
        transactionDTO.setPostingTransactionCode(authRecordedDTO.getPostingTransactionCodeRev());
        transactionDTO.setPostingTransactionCodeRev(authRecordedDTO.getPostingTransactionCode());

        if(StringUtils.isEmpty(authRecordedDTO.getPostingTransactionCodeRev())
                || String.valueOf(AuthConstans.ZERO).equals(authRecordedDTO.getPostingTransactionCodeRev())){
            transactionDTO.setPostIndicator(RecordedEnum.BATCH.getCode());
        }else{
            //对于需要实时入账 状态设置为 已处理
            transactionDTO.setPostFlag(PostFlagEnum.PROCESSED.getCode());
            transactionDTO.setPostIndicator(RecordedEnum.REAL_TIME.getCode());
            transactionDTO.setTrasactionStatus(TrasactionStatusEnum.MATCHED.getCode());
        }
        //针对调整交易的内生撤销交易修改，该笔撤销交易状态修改为CANCEL
        boolean adjustmentCancelByUsFlag = StringUtils.equals(authRecordedDTO.getAuthProcessingCode(),AuthProcessingCodeEnum.ADJUSTMENT_CODE.getCode()) &&
                StringUtils.equals(authRecordedDTO.getAuthReversalType(),ReversalTypeEnum.REVOCATION_TRANS.getCode());
        if(adjustmentCancelByUsFlag){
            transactionDTO.setTrasactionStatus(TrasactionStatusEnum.CANCEL.getCode());
        }
        //授权上下文中存放本次交易的未并账信息,入账用
        authorizationCheckProcessingPayload.setOutstandingTransactionDTO(transactionDTO);
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateOutstandingTransaction(transactionDTO, false);
            return 1;
        } else {
            return outstandingTransService.insert(transactionDTO);
        }
    }

    /**
     * OutstandingTransaction set value;
     * @return {@link OutstandingTransactionDTO}
     */
    private OutstandingTransactionDTO outstandingTransactionSet(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AuthRecordedDTO authRecordedDTO,
                                                                OrganizationInfoResDTO orgInfo, AuthorisationProcessingResDTO authorProcessInfo){
        OutstandingTransactionDTO outstandingTransactionDTO = new OutstandingTransactionDTO();
        outstandingTransactionDTO.setOutstandingTransactionId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        outstandingTransactionDTO.setAcctProductCode(authRecordedDTO.getPrimaryAccountGroupAuthControlDTO().getAcctProductCode());
        outstandingTransactionDTO.setCardNumber(authRecordedDTO.getAuthCardNumber());
        outstandingTransactionDTO.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        outstandingTransactionDTO.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        outstandingTransactionDTO.setOrganizationNumber(orgInfo.getOrganizationNumber());
        String productNumber = authorizationCheckProcessingPayload.getCardAuthorizationDTO().getProductNumber();
        outstandingTransactionDTO.setProductNumber(productNumber);
        outstandingTransactionDTO.setCustomerId(authRecordedDTO.getAuthCustomerId());
        outstandingTransactionDTO.setAuthorizationTransactionCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        LocalDate releaseDate = orgInfo.getNextProcessingDay().plusDays(authorProcessInfo.getAuthorisationRemainDays());
        outstandingTransactionDTO.setTransactionReleaseDate(releaseDate);
        outstandingTransactionDTO.setTransactionRemainDays(Long.valueOf(authorProcessInfo.getAuthorisationRemainDays()));
//        outstandingTransactionDTO.setBusinessDate(orgInfo.getNextProcessingDay());
        // update 2022-12-07
        LocalDate nextProcessingDay = orgInfo.getNextProcessingDay();
        String f15 = authRecordedDTO.getAuthSettlementDate();
        if (StringUtils.isNotBlank(f15) && f15.length() == 4) {
            if (f15.startsWith("01") && nextProcessingDay.getMonthValue() == 12) {
                nextProcessingDay = LocalDate.of(nextProcessingDay.getYear()+1, Integer.parseInt(f15.substring(0,2)), Integer.parseInt(f15.substring(2,4)));
            } else {
                nextProcessingDay = LocalDate.of(nextProcessingDay.getYear(), Integer.parseInt(f15.substring(0,2)), Integer.parseInt(f15.substring(2,4)));
            }
        }
        outstandingTransactionDTO.setBusinessDate(nextProcessingDay);

        //设置分区号
        int partitionKeyGenerator = partitionKeyInitService.partitionKeyGenerator(
                authorizationCheckProcessingPayload.getCardAuthorizationDTO(), null);
        outstandingTransactionDTO.setPartitionKey((long) partitionKeyGenerator);
        //交易时间特殊处理
        LocalDateTime localDateTime = ApplicationManager.getTransTimeOfUpi(orgInfo, authRecordedDTO);
        outstandingTransactionDTO.setTransactionDate(localDateTime.toLocalDate());
        outstandingTransactionDTO.setTransactionTime(localDateTime);
        outstandingTransactionDTO.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        outstandingTransactionDTO.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        outstandingTransactionDTO.setBillingConversionRate(authRecordedDTO.getAuthCardholderBillingRate()==null?null:authRecordedDTO.getAuthCardholderBillingRate().toString());
        BigDecimal settlementAmount = authRecordedDTO.getAuthTransactionSettlementAmount();
        outstandingTransactionDTO.setSettlementAmount(settlementAmount == null || settlementAmount.compareTo(BigDecimal.ZERO) == 0 ? authRecordedDTO.getAuthCardholderBillingAmount() : settlementAmount);
        outstandingTransactionDTO.setSettlementConversionRate(authRecordedDTO.getSettlementConversionRate());
        outstandingTransactionDTO.setSettlementCurrencyCode(settlementAmount == null || settlementAmount.compareTo(BigDecimal.ZERO) == 0 ? authRecordedDTO.getAuthBillingCurrencyCode() : authRecordedDTO.getSettlementCurrencyCode());
        outstandingTransactionDTO.setOutstandingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        outstandingTransactionDTO.setMessageTypeIndicator(authRecordedDTO.getAuthMessageTypeId());
        outstandingTransactionDTO.setProcessingCode(authRecordedDTO.getAuthProcessingCode());
        outstandingTransactionDTO.setTraceNumber(authRecordedDTO.getAuthSystemTraceAuditNumber());
        outstandingTransactionDTO.setOriginalSystemDatetime(authRecordedDTO.getAuthTransmissionTime());
        outstandingTransactionDTO.setExpireDate(authRecordedDTO.getAuthCardExpirationDate());
        outstandingTransactionDTO.setForwardInstituteId(authRecordedDTO.getAuthForwardingIdentificationCode());
        outstandingTransactionDTO.setAcquireInstituteId(authRecordedDTO.getAuthAcquiringIdentificationCode());
        outstandingTransactionDTO.setRetrievalReferenceNumber(authRecordedDTO.getAuthRetrievalReferenceNumber());
        outstandingTransactionDTO.setAuthorizationCode(authRecordedDTO.getAuthAuthCode());
        outstandingTransactionDTO.setResponseCode(authRecordedDTO.getAuthResponseCode());
        outstandingTransactionDTO.setMcc(authRecordedDTO.getAuthMerchantType());
        outstandingTransactionDTO.setCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        outstandingTransactionDTO.setMerchantId(authRecordedDTO.getAuthCardAcceptorIdentification());
        outstandingTransactionDTO.setMerchantName(authRecordedDTO.getAuthCardAcceptorNameLocation());
        outstandingTransactionDTO.setTerminalId(authRecordedDTO.getAuthCardAcceptorTerminalCode());
//        String posEntryMode = authRecordedDTO.getAuthCardSequenceNumber() + authRecordedDTO.getAuthServicePointConditionCode();
        outstandingTransactionDTO.setPosEntryMode(authRecordedDTO.getAuthServicePointCardCode()+authRecordedDTO.getAuthServicePointPinCode());
        outstandingTransactionDTO.setCardSequence(authRecordedDTO.getAuthCardSequenceNumber() != null ? Long.parseLong(authRecordedDTO.getAuthCardSequenceNumber()) : null);
        outstandingTransactionDTO.setPosConditionCode(authRecordedDTO.getAuthServicePointConditionCode());
        // 2023-04-03 入账交易处理
        String f60 = authRecordedDTO.getField60TxnData();
        if (StringUtils.isNotEmpty(f60) && f60.length() >= 15) {
            outstandingTransactionDTO.setPosEntryCapability(f60.substring(4, 15));
            if (f60.length() >= 16) {
                outstandingTransactionDTO.setTransactionDescription(f60.substring(15));
            }
        }
        outstandingTransactionDTO.setCreditLimitAmount(BigDecimal.ZERO);
        outstandingTransactionDTO.setOnlinePaymentAmount(BigDecimal.ZERO);
        outstandingTransactionDTO.setOverpaymentAmount(BigDecimal.ZERO);
        outstandingTransactionDTO.setTrasactionStatus(TrasactionStatusEnum.UNMATCHED.getCode());
        outstandingTransactionDTO.setPostFlag(PostFlagEnum.UNPROCESSED.getCode());
        outstandingTransactionDTO.setCreateTime(LocalDateTime.now());
        outstandingTransactionDTO.setUpdateTime(LocalDateTime.now());
        outstandingTransactionDTO.setUpdateBy(Constant.DEFAULT_USER);
        outstandingTransactionDTO.setVersionNumber(1L);
        outstandingTransactionDTO.setTransactionSource(authRecordedDTO.getAuthTransactionSourceCode());
        outstandingTransactionDTO.setBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        outstandingTransactionDTO.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
        //outstandingTransactionDTO.setCreditLimitNodeId(limitDTO.getLimitTypeNum())
        //分期字段
        outstandingTransactionDTO.setInstalmentProduct(authRecordedDTO.getInstallmentProductCode());
        outstandingTransactionDTO.setInstallmentType(authRecordedDTO.getInstallmentType());
        outstandingTransactionDTO.setTerm(authRecordedDTO.getTerm());
        outstandingTransactionDTO.setOriginTransactionId(authRecordedDTO.getOriginTransactionId());
        outstandingTransactionDTO.setAcquireReferenceNo(authRecordedDTO.getAcquireReferenceNo());
        outstandingTransactionDTO.setInstallmentPriceFlag(authRecordedDTO.getInstallmentPriceFlag());
        outstandingTransactionDTO.setInstallmentTotalFee(authRecordedDTO.getInstallmentTotalFee());
        outstandingTransactionDTO.setInstallmentFeeRate(authRecordedDTO.getInstallmentFeeRate());
        outstandingTransactionDTO.setInstallmentDerateMethod(authRecordedDTO.getInstallmentDerateMethod());
        outstandingTransactionDTO.setInstallmentDerateValue(authRecordedDTO.getInstallmentDerateValue());
        outstandingTransactionDTO.setMerchantId(authRecordedDTO.getMerchantId());
        outstandingTransactionDTO.setTransactionDesc(authRecordedDTO.getTransactionDesc());
        outstandingTransactionDTO.setAuthTransactionTypeTop(authRecordedDTO.getAuthTransactionTypeTopCode());
        outstandingTransactionDTO.setAuthTransactionTypeDetail(authRecordedDTO.getAuthTransactionTypeDetailCode());

        outstandingTransactionDTO.setTerminalType(authRecordedDTO.getTerminalType());
        outstandingTransactionDTO.setSenderAccount(authRecordedDTO.getSenderAccount());
        outstandingTransactionDTO.setSenderName(authRecordedDTO.getSenderName());
        outstandingTransactionDTO.setSenderAccountBankNumber(authRecordedDTO.getSenderAccountBankNumber());
        outstandingTransactionDTO.setSendingOrganizationChineseAbbreviation(authRecordedDTO.getSendingOrganizationChineseAbbreviation());
        outstandingTransactionDTO.setTurnOutPartyCardNumber(authRecordedDTO.getTurnOutPartyCardNumber());
        outstandingTransactionDTO.setTransferPartyCardNumber(authRecordedDTO.getTransferPartyCardNumber());
        outstandingTransactionDTO.setTransfereeName(authRecordedDTO.getTransfereeName());
        outstandingTransactionDTO.setTurnOutPartyName(authRecordedDTO.getTurnOutPartyName());
        outstandingTransactionDTO.setClientIpAddress(authRecordedDTO.getClientIpAddress());
        outstandingTransactionDTO.setMacAddress(authRecordedDTO.getMacAddress());
        outstandingTransactionDTO.setMerchantInfo2ndSort(authRecordedDTO.getMerchantInfo2ndSort());
        outstandingTransactionDTO.setTradingAddress(authRecordedDTO.getTradingAddress());
        outstandingTransactionDTO.setNetworkReferenceId(authRecordedDTO.getAuthNetworkReferenceId());

//        outstandingTransactionDTO.setLimitUnitCode(authRecordedDTO.getLimitUnitCode());
//        outstandingTransactionDTO.setLimitUnitVersion(authRecordedDTO.getLimitUnitVersion());
        //借贷分离-未并账交易表删除额度管控单元编号及版本号字段，增加额度管控单元JSON（存放一组额度管控单元）

        //新增分期详细信息字段 JSON格式
        if(StringUtils.isNotEmpty(authRecordedDTO.getAuthReconciliationTotals())){
            Map<String, Object> map = Field63Utils.parseInstallment(authRecordedDTO.getAuthReconciliationTotals());
            String installmentInfo = JSON.toJSONString(map);
            outstandingTransactionDTO.setInstallmentInfo(installmentInfo);
        }
        List<CalculateLimitUnitDTO> limitUnitList = authRecordedDTO.getLimitUnitList();
        if (!CollectionUtils.isEmpty(limitUnitList)){
            outstandingTransactionDTO.setLimitUnitJson(JSONArray.toJSONString(limitUnitList));
        }
        outstandingTransactionDTO.setTransCtrlUnitId(authRecordedDTO.getTransCtrlUnitId());
        // 3.对于UPI渠道交易，授权处理写未并账时，应给“单双信息交易标识”字段根据MTI进行赋值，01XX赋值为双信息，02XX赋值为单信息
        if (Objects.equals(authRecordedDTO.getAuthTransactionSourceCode(), AuthTransactionSourceCodeEnum.UPI.getCode())) {
            String mti = authRecordedDTO.getAuthMessageTypeId();
            if (mti.startsWith("01")) {
                outstandingTransactionDTO.setSingleDoubleType(2);
            } else if (mti.startsWith("02")) {
                outstandingTransactionDTO.setSingleDoubleType(1);
            }
        }
        //记录markup fee
        outstandingTransactionDTO.setAuthMarkupFee(Optional.ofNullable(authRecordedDTO.getAuthMarkupFee()).orElse(BigDecimal.ZERO));
        return  outstandingTransactionDTO;
    }

    /**
     * visa 授权流水表的更新
     * @param authRecordedDTO {@link AuthRecordedDTO}
     */
    @BatchSharedAnnotation
    private void visaAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogVisa authorizationLog;
        AuthorizationLogVisa orignalAuthorizationLog ;
        //本交易为撤销交易(包含部分撤销)(authTransactionTypeCode为2)
        String authTransactionTypeCode = authRecordedDTO.getAuthTransactionTypeCode();

        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getAuthorizationLogVisaByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogVisa(BeanMapping.copy(authorizationLog, AuthorizationLogVisaDTO.class), true);
                } else {
                    authorizationLogVisaMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.error("CUP revocation operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        //本交易为冲正交易(包含部分冲正)(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getAuthorizationLogVisaByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogVisa(BeanMapping.copy(authorizationLog, AuthorizationLogVisaDTO.class), true);
                } else {
                    authorizationLogVisaMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.error("CUP reversal operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getAuthorizationLogVisaByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogVisa(BeanMapping.copy(authorizationLog, AuthorizationLogVisaDTO.class), true);
                } else {
                    authorizationLogVisaMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.error("CUP revocation reversal operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }

            orignalAuthorizationLog = getAuthorizationLogVisaByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (orignalAuthorizationLog != null) {
                orignalAuthorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                orignalAuthorizationLog.setReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                orignalAuthorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogVisa(BeanMapping.copy(orignalAuthorizationLog, AuthorizationLogVisaDTO.class), true);
                } else {
                    authorizationLogVisaMapper.updateByPrimaryKeySelective(orignalAuthorizationLog);
                }
            } else {
                logger.error("CUP revocation reversal operation, original transaction log not found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
    }

    /**
     * visa 授权流水表的更新
     * @param authRecordedDTO {@link AuthRecordedDTO}
     */
    @BatchSharedAnnotation
    private void epccAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogEpcc authorizationLog;
        AuthorizationLogEpcc orignalAuthorizationLog ;
        //本交易为撤销交易(包含部分撤销)(authTransactionTypeCode为2)
        String authTransactionTypeCode = authRecordedDTO.getAuthTransactionTypeCode();

        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getAuthorizationLogEpccByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogVisa(BeanMapping.copy(authorizationLog, AuthorizationLogVisaDTO.class), true);
                } else {
                    authorizationLogEpccSelfMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.error("For the EPCC cancellation operation, the original transaction record cannot be found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        //本交易为冲正交易(包含部分冲正)(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getAuthorizationLogEpccByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogEpcc(BeanMapping.copy(authorizationLog, AuthorizationLogEpccDTO.class), true);
                } else {
                    authorizationLogEpccSelfMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.error("For the CUP reversal operation, the original transaction record cannot be found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLog = getAuthorizationLogEpccByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLog != null) {
                authorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLog.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogEpcc(BeanMapping.copy(authorizationLog, AuthorizationLogEpccDTO.class), true);
                } else {
                    authorizationLogEpccSelfMapper.updateByPrimaryKeySelective(authorizationLog);
                }
            } else {
                logger.error("For the EPCC cancellation and reversal operation, the original transaction record cannot be found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }

            orignalAuthorizationLog = getAuthorizationLogEpccByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (orignalAuthorizationLog != null) {
                orignalAuthorizationLog.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                orignalAuthorizationLog.setReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                orignalAuthorizationLog.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogEpcc(BeanMapping.copy(orignalAuthorizationLog, AuthorizationLogEpccDTO.class), true);
                } else {
                    authorizationLogEpccSelfMapper.updateByPrimaryKeySelective(orignalAuthorizationLog);
                }
            } else {
                logger.error("Original transaction record not found during EPCC cancellation and reversal operation!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
    }

    /**
     * jcb 授权流水表的更新
     * @param authRecordedDTO {@link AuthRecordedDTO}
     */
    @BatchSharedAnnotation
    private void jcbAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogJcb authorizationLogJcb;
        AuthorizationLogJcb orignalAuthorizationLogJcb ;
        //本交易为撤销交易(包含部分撤销)(authTransactionTypeCode为2)
        String authTransactionTypeCode = authRecordedDTO.getAuthTransactionTypeCode();

        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLogJcb = getAuthorizationLogJcbByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (authorizationLogJcb != null) {
                authorizationLogJcb.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
                authorizationLogJcb.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLogJcb.setUpdateTime(LocalDateTime.now());
                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogJcb(BeanMapping.copy(authorizationLogJcb, AuthorizationLogJcbDTO.class), true);
                } else {
                    authorizationLogJcbMapper.updateByPrimaryKeySelective(authorizationLogJcb);
                }
            } else {
                logger.error("For the JCB cancellation operation, the original transaction record cannot be found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        //本交易为冲正交易(包含部分冲正)(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLogJcb = getAuthorizationLogJcbByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLogJcb != null) {
                authorizationLogJcb.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLogJcb.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLogJcb.setUpdateTime(LocalDateTime.now());
                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogJcb(BeanMapping.copy(authorizationLogJcb, AuthorizationLogJcbDTO.class), true);
                } else {
                    authorizationLogJcbMapper.updateByPrimaryKeySelective(authorizationLogJcb);
                }
            } else {
                logger.error("Original transaction record not found during JCB reversal operation!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLogJcb = getAuthorizationLogJcbByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            if (authorizationLogJcb != null) {
                authorizationLogJcb.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLogJcb.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLogJcb.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogJcb(BeanMapping.copy(authorizationLogJcb, AuthorizationLogJcbDTO.class), true);
                } else {
                    authorizationLogJcbMapper.updateByPrimaryKeySelective(authorizationLogJcb);
                }

            } else {
                logger.error("Original transaction record not found during JCB reversal cancellation operation!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
            orignalAuthorizationLogJcb = getAuthorizationLogJcbByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());

            if (orignalAuthorizationLogJcb != null) {
                orignalAuthorizationLogJcb.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                orignalAuthorizationLogJcb.setReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                orignalAuthorizationLogJcb.setUpdateTime(LocalDateTime.now());
                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogJcb(BeanMapping.copy(authorizationLogJcb, AuthorizationLogJcbDTO.class), true);
                } else {
                    authorizationLogJcbMapper.updateByPrimaryKeySelective(orignalAuthorizationLogJcb);
                }
            } else {
                logger.error("Original transaction record not found when performing JCB reversal cancellation operation!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
    }

    /**
     * mc 授权流水表的更新
     * @param authRecordedDTO {@link AuthRecordedDTO}
     */
    private void mcAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogMc authorizationLogMc;
        AuthorizationLogMc orignalAuthorizationLogMc ;

        //本交易为撤销交易(包含部分撤销)(authTransactionTypeCode为2)
        String authTransactionTypeCode = authRecordedDTO.getAuthTransactionTypeCode();

        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLogMc = getAuthorizationLogMcByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());

            if (authorizationLogMc != null) {
                authorizationLogMc.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
                authorizationLogMc.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLogMc.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogMc(BeanMapping.copy(authorizationLogMc, AuthorizationLogMcDTO.class), true);
                } else {
                    authorizationLogMcMapper.updateByPrimaryKeySelective(authorizationLogMc);
                }
            } else {
                logger.error("MC cancellation operation: Original transaction flow cannot be found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        //本交易为冲正交易(包含部分冲正)(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLogMc = getAuthorizationLogMcByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());

            if (authorizationLogMc != null) {
                authorizationLogMc.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLogMc.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLogMc.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogMc(BeanMapping.copy(authorizationLogMc, AuthorizationLogMcDTO.class), true);
                } else {
                    authorizationLogMcMapper.updateByPrimaryKeySelective(authorizationLogMc);
                }

            } else {
                logger.error("MC reversal operation: Original transaction flow cannot be found!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authTransactionTypeCode)) {
            authorizationLogMc = getAuthorizationLogMcByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());

            if (authorizationLogMc != null) {
                authorizationLogMc.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLogMc.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authorizationLogMc.setUpdateTime(LocalDateTime.now());

                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogMc(BeanMapping.copy(authorizationLogMc, AuthorizationLogMcDTO.class), true);
                } else {
                    authorizationLogMcMapper.updateByPrimaryKeySelective(authorizationLogMc);
                }

            } else {
                logger.error("Original transaction record not found during MC reversal cancellation operation!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
            orignalAuthorizationLogMc = getAuthorizationLogMcByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            if (orignalAuthorizationLogMc != null) {
                orignalAuthorizationLogMc.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                orignalAuthorizationLogMc.setReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogMc(BeanMapping.copy(orignalAuthorizationLogMc, AuthorizationLogMcDTO.class), true);
                } else {
                    authorizationLogMcMapper.updateByPrimaryKeySelective(orignalAuthorizationLogMc);
                }
            } else {
                logger.error("Original transaction record not found when performing MC reversal cancellation operation!");
                authRecordedDTO.setAuthResponseCode(AuthConstans.AUTHRESPONSECODE);
            }
        }
    }

    /**
     * rule-3 授权流水表add
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @return int
     */
    @BatchSharedAnnotation
    public int addAuthorizationLog(AuthRecordedDTO authRecordedDTO) {
        int res = 0;
        if(AuthTransactionSourceCodeEnum.UPI.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
            res =  cupAddAuthLog(authRecordedDTO);
        }
        //theBank
        if (AuthTransactionSourceCodeEnum.THE_BANK.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())){
            res =  cupAddAuthLog(authRecordedDTO);
        }
        //visa
        if (AuthTransactionSourceCodeEnum.VISA.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
            res = visaAddAuthLog(authRecordedDTO);
        }
        //MC
        if (AuthTransactionSourceCodeEnum.MASTERCARD.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
            res =  mcAddAuthLog(authRecordedDTO);
        }
        //JCB
        if (AuthTransactionSourceCodeEnum.JCB.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())) {
            res = jcbAddAuthLog(authRecordedDTO);
        }
        //Expess
        if (Objects.equals(AuthTransactionSourceCodeEnum.EXPRESS.getCode(),authRecordedDTO.getAuthTransactionSourceCode())){
            res = expressAddAuthLog(authRecordedDTO);
        }
        return res;
    }

    @BatchSharedAnnotation
    public int expressAddAuthLog(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogExpress authorizationLogExpress = new AuthorizationLogExpress();
        authorizationLogExpress.setAuthLogId(Optional.ofNullable(authRecordedDTO.getCurrentAuthLogId()).orElse(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        authorizationLogExpress.setAuthLogTime(LocalDateTime.now());
        authorizationLogExpress.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        authorizationLogExpress.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        authorizationLogExpress.setMessageTypeId(authRecordedDTO.getAuthMessageTypeId());
        authorizationLogExpress.setCardNumber(authRecordedDTO.getAuthCardNumber());
        authorizationLogExpress.setProcessingCode(authRecordedDTO.getAuthProcessingCode());
        authorizationLogExpress.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        authorizationLogExpress.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        authorizationLogExpress.setTransmissionTime(authRecordedDTO.getAuthTransmissionTime());
        authorizationLogExpress.setCardholderBillingRate(authRecordedDTO.getAuthCardholderBillingRate()==null
                ? null:authRecordedDTO.getAuthCardholderBillingRate().intValue());
        authorizationLogExpress.setSystemTraceAuditNumber(authRecordedDTO.getAuthSystemTraceAuditNumber());
        authorizationLogExpress.setLocalTransactionDate(authRecordedDTO.getAuthLocalTransactionDate());
        authorizationLogExpress.setLocalTransactionTime(authRecordedDTO.getAuthLocalTransactionTime());
        authorizationLogExpress.setCardExpirationDate(authRecordedDTO.getAuthCardExpirationDate());
        authorizationLogExpress.setSettlementDate(authRecordedDTO.getAuthSettlementDate());
        authorizationLogExpress.setMerchantType(authRecordedDTO.getAuthMerchantType());
        authorizationLogExpress.setMerchantCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        authorizationLogExpress.setServicePointCardCode(authRecordedDTO.getAuthServicePointCardCode());
        authorizationLogExpress.setServicePointPinCode(authRecordedDTO.getAuthServicePointPinCode());
        authorizationLogExpress.setCardSequenceNumber(authRecordedDTO.getAuthCardSequenceNumber());
        authorizationLogExpress.setServicePointConditionCode(authRecordedDTO.getAuthServicePointConditionCode());
        authorizationLogExpress.setTransactionFeeIndicator(authRecordedDTO.getAuthTransactionFeeIndicator());
        authorizationLogExpress.setTransactionFee(authRecordedDTO.getAuthTransactionFee());
        authorizationLogExpress.setAcquiringIdentificationCode(authRecordedDTO.getAuthAcquiringIdentificationCode());
        authorizationLogExpress.setForwardingIdentificationCode(authRecordedDTO.getAuthForwardingIdentificationCode());
        authorizationLogExpress.setTrack2Data(authRecordedDTO.getAuthTrack2Data());
        authorizationLogExpress.setRetrievalReferenceNumber(authRecordedDTO.getAuthRetrievalReferenceNumber());

        //
        authorizationLogExpress.setAuthIdentificationResponse(authRecordedDTO.getAuthAuthIdentificationResponse());

        authorizationLogExpress.setResponseCode(authRecordedDTO.getAuthResponseCode());
        authorizationLogExpress.setCardAcceptorTerminalCode(authRecordedDTO.getAuthCardAcceptorTerminalCode());
        authorizationLogExpress.setCardAcceptorIdentification(authRecordedDTO.getAuthCardAcceptorIdentification());
        authorizationLogExpress.setCardAcceptorNameLocation(authRecordedDTO.getAuthCardAcceptorNameLocation());
        authorizationLogExpress.setTrack1Data(authRecordedDTO.getAuthTrack1Data());
        authorizationLogExpress.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        authorizationLogExpress.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
        authorizationLogExpress.setPinData(authRecordedDTO.getAuthPinData());
        authorizationLogExpress.setIccSystemRelatedData(authRecordedDTO.getAuthIccSystemRelatedData());
        authorizationLogExpress.setTerminalType(authRecordedDTO.getAuthTerminalType());
        authorizationLogExpress.setNoCardVerificationValue(authRecordedDTO.getAuthNoCardVerificationValue());
        authorizationLogExpress.setOriginalSystemTraceNumber(authRecordedDTO.getAuthOriginalSystemTraceAuditNumber());
        authorizationLogExpress.setOriginalTransmissionTime(authRecordedDTO.getAuthOriginalTransmissionTime());
        authorizationLogExpress.setOriginalAcquiringIdeCode(authRecordedDTO.getAuthOriginalAcquiringIdentificationCode());
        authorizationLogExpress.setOriginalForwardingIdeCode(authRecordedDTO.getAuthOriginalForwardingIdentificationCode());
        authorizationLogExpress.setAuthTypeCode(authRecordedDTO.getAuthAuthTypeCode());

        authorizationLogExpress.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());

        authorizationLogExpress.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        authorizationLogExpress.setAuthCode(authRecordedDTO.getAuthAuthCode());
        authorizationLogExpress.setErrorCode(authRecordedDTO.getErrorDetailCode());
        authorizationLogExpress.setCardActivationResultCode(authRecordedDTO.getAuthCardActivationResultCode());
        authorizationLogExpress.setCardStatusResultCode(authRecordedDTO.getAuthCardStatusResultCode());
        authorizationLogExpress.setCardExpireDateResultCode(authRecordedDTO.getAuthCardExpireDateResultCode());
        authorizationLogExpress.setCardLimitResultCode(authRecordedDTO.getAuthCardLimitResultCode());
        authorizationLogExpress.setCardBlockResultCode(authRecordedDTO.getAuthCardBlockResultCode());
        authorizationLogExpress.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogExpress.setCustomerBlockResultCode(authRecordedDTO.getAuthCustomerBlockResultCode());
        authorizationLogExpress.setFraudFileResultCode(authRecordedDTO.getAuthFraudFileResultCode());
        authorizationLogExpress.setTerminalBlacklistResultCode(authRecordedDTO.getAuthTerminalBlacklistResultCode());
        authorizationLogExpress.setCvvResultCode(authRecordedDTO.getAuthCvvResultCode());
        authorizationLogExpress.setAvailLimitResultCode(authRecordedDTO.getAuthAvailLimitResultCode());
        authorizationLogExpress.setVelocityCheckResultCode(authRecordedDTO.getAuthVelocityCheckResultCode());
        authorizationLogExpress.setUsageCardResultCode(authRecordedDTO.getAuthUsageCardResultCode());
        authorizationLogExpress.setSingleLimitResultCode(authRecordedDTO.getAuthSingleLimitResultCode());
        authorizationLogExpress.setMaxApproveNmbrResultCode(authRecordedDTO.getAuthMaxApproveNmbrResultCode());
        authorizationLogExpress.setMaxDeclineNmbrResultCode(authRecordedDTO.getAuthMaxDeclineNmbrResultCode());
        //中间变量STATUS
        authorizationLogExpress.setTrancactionStatus(authRecordedDTO.getAuthTrancactionStatus());
        //中间变量TYPE
        authorizationLogExpress.setReversalType(authRecordedDTO.getAuthReversalType());
        authorizationLogExpress.setAuthTransactionSourceCode(authRecordedDTO.getAuthTransactionSourceCode());
        //《授权额度更新》处理后获取的limit_type_num
        authorizationLogExpress.setCreditLimitNodeId(authRecordedDTO.getAuthCreditLimitNodeId());
        authorizationLogExpress.setCustomerId(authRecordedDTO.getAuthCustomerId());
        authorizationLogExpress.setTransactionTypeCode(authRecordedDTO.getAuthTransactionTypeCode());
        authorizationLogExpress.setPartitionKey("");
        authorizationLogExpress.setCreateTime(LocalDateTime.now());
        authorizationLogExpress.setUpdateTime(LocalDateTime.now());
        authorizationLogExpress.setUpdateBy(Constant.DEFAULT_USER);
        authorizationLogExpress.setVersionNumber(1L);
        //新增字段
        authorizationLogExpress.setInteractiveModeFlag(authRecordedDTO.getAuthInteractiveModeFlag());
        authorizationLogExpress.setTransactionMedia(authRecordedDTO.getAuthTransactionMedia());
        authorizationLogExpress.setIdType(authRecordedDTO.getAuthIdType());
        authorizationLogExpress.setIdNumber(authRecordedDTO.getAuthIdNumber());
        authorizationLogExpress.setFormatId(authRecordedDTO.getAuthFormatId());
        authorizationLogExpress.setName(authRecordedDTO.getAuthName());
        authorizationLogExpress.setMobileNo(authRecordedDTO.getAuthMobileNo());
        authorizationLogExpress.setAccountStatusResultCode(authRecordedDTO.getAuthAccountStatusResultCode());
        authorizationLogExpress.setCustomerStatusResultCode(authRecordedDTO.getAuthCustomerStatusResultCode());
        authorizationLogExpress.setPinBlockResultCode(authRecordedDTO.getAuthPinBlockResultCode());
        authorizationLogExpress.setCvv2ResultCode(authRecordedDTO.getAuthCvv2ResultCode());
        authorizationLogExpress.setFisrtUsageResultCode(authRecordedDTO.getAuthFisrtUsageResultCode());
        authorizationLogExpress.setIdTypeResultCode(authRecordedDTO.getAuthIdTypeResultCode());
        authorizationLogExpress.setIdNumberResultCode(authRecordedDTO.getAuthIdNumberResultCode());
        authorizationLogExpress.setNameResultCode(authRecordedDTO.getAuthNameResultCode());
        authorizationLogExpress.setMobileNoResultCode(authRecordedDTO.getAuthMobileNoResultCode());
        authorizationLogExpress.setInternalSpecialResultCode(authRecordedDTO.getAuthInnerSpecialResultCode());
        authorizationLogExpress.setInternetSwitchResultCode(authRecordedDTO.getAuthInternetTransactionResultCode());
        authorizationLogExpress.setInternalCashSwResultCode(authRecordedDTO.getAuthInternalCashResultCode());
        authorizationLogExpress.setExternalCashSwResultCode(authRecordedDTO.getAuthExternalCashResultCode());
        authorizationLogExpress.setExternalSwitchResultCode(authRecordedDTO.getAuthExternalTransResultCode());
        authorizationLogExpress.setAtmSwitchResultCode(authRecordedDTO.getAuthAtmSwitchResultCode());
        authorizationLogExpress.setExistCardSwitchResultCode(authRecordedDTO.getAuthExistCardSwitchResultCode());
        authorizationLogExpress.setNoCardSwitchResultCode(authRecordedDTO.getAuthNoCardSwitchResultCode());
        authorizationLogExpress.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogExpress.setAdditionalAmounts(authRecordedDTO.getAuthAdditionalAmounts());
        //实时入账交易码,实时入账交易码(冲正和撤销用)
        authorizationLogExpress.setPostingTransactionCode(authRecordedDTO.getPostingTransactionCode());
        authorizationLogExpress.setPostingTransactionCodeRev(authRecordedDTO.getPostingTransactionCodeRev());
        authorizationLogExpress.setMerchantFraudResultCode(authRecordedDTO.getMerchantFraudResultCode());
        authorizationLogExpress.setCardSafetyLockResultCode(authRecordedDTO.getAuthCardSecuritySwitchResultCode());
        authorizationLogExpress.setOpponentAccountName(authRecordedDTO.getOpponentAccountName());
        authorizationLogExpress.setOpponentAccountNumber(authRecordedDTO.getOpponentAccountNumber());
        authorizationLogExpress.setOpponentBankId(authRecordedDTO.getOpponentBankId());
        authorizationLogExpress.setOpponentTxnArea(authRecordedDTO.getOpponentTxnArea());
        authorizationLogExpress.setMerchantInfo2ndCode(authRecordedDTO.getMerchantInfo2ndCode());
        authorizationLogExpress.setMerchantInfo2ndName(authRecordedDTO.getMerchantInfo2ndName());
        authorizationLogExpress.setOpponentTxnArea1(authRecordedDTO.getOpponentTxnArea1());
        authorizationLogExpress.setOpponentTxnArea2(authRecordedDTO.getOpponentTxnArea2());
        authorizationLogExpress.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        authorizationLogExpress.setAnyCloudFraudResultCode(authRecordedDTO.getAuthAnyCloudFraudResultCode());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogExpress(BeanMapping.copy(authorizationLogExpress, AuthorizationLogExpressDTO.class), false);
            return 1;
        } else {
            return authorizationLogExpressMapper.insertSelective(authorizationLogExpress);
        }
    }

    /**
     * 预授权流水表新增
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */

    public int addPreAuthorizationLog(AuthRecordedDTO authRecordedDTO,AccountManagementInfoDTO accountManagementInfoDTO,OrganizationInfoResDTO organizationInfoResDTO,AuthorisationProcessingResDTO authorProcessInfo) {
        //预授权流水新增
        return cupAddPreAuthLog(authRecordedDTO,accountManagementInfoDTO, organizationInfoResDTO,authorProcessInfo);
    }

    @BatchSharedAnnotation
    public int addManagementAuthLog(AuthRecordedDTO authRecordedDTO){
        ManagementAuthLogDTO managementAuthLogDTO = new ManagementAuthLogDTO();
        managementAuthLogDTO.setManagementauthLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        managementAuthLogDTO.setMti(authRecordedDTO.getAuthMessageTypeId());
        managementAuthLogDTO.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        managementAuthLogDTO.setTransmissionTime(authRecordedDTO.getAuthTransmissionTime());
        managementAuthLogDTO.setLocalTransactionTime(authRecordedDTO.getAuthLocalTransactionTime());
        managementAuthLogDTO.setAuthAcqNumber(authRecordedDTO.getAuthTransDestInstitutionIdentificationCode());
        managementAuthLogDTO.setAuthForwardingNumber(authRecordedDTO.getAuthTransOriginatorInstitutionIdentificationCode());
        managementAuthLogDTO.setAuthSystemTrace(authRecordedDTO.getAuthSystemTraceAuditNumber());
        managementAuthLogDTO.setFuctionCode(authRecordedDTO.getAuthFunctionCode());
        managementAuthLogDTO.setActionCode(authRecordedDTO.getAuthResponseCode());
        managementAuthLogDTO.setUpdateTime(LocalDateTime.now());
        managementAuthLogDTO.setCreateTime(LocalDateTime.now());
        managementAuthLogDTO.setUpdateBy(Constant.DEFAULT_USER);
        managementAuthLogDTO.setVersionNumber(1L);

        ManagementAuthLog managementAuthLog = BeanMapping.copy(managementAuthLogDTO, ManagementAuthLog.class);
        return managementAuthLogMapper.insertSelective(managementAuthLog);
//        if (CustAccountBO.isBatch()) {
//            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogExpress(BeanMapping.copy(authorizationLogExpress, AuthorizationLogExpressDTO.class), false);
//            return 1;
//        } else {
//            return managementAuthLogMapper.insertSelective(managementAuthLog);
//        }
    }

    /**
     * 预授权流水表更新
     * 针对预授权请求
     * @param authRecordedDTO AuthRecordedDTO
     * @param accountManagementInfoDTO  AccountManagementInfoDTO
     * @return int
     */
    @BatchSharedAnnotation
    public int modifyPreAuthorizationLog(AuthRecordedDTO authRecordedDTO, AccountManagementInfoDTO accountManagementInfoDTO,OrganizationInfoResDTO organizationInfoResDTO,AuthorisationProcessingResDTO authorProcessInfo) {
        //1.如果是冲正、撤销、撤销冲正交易，还需要更新原交易的授权流水记录。
        boolean flag = AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                ||AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                ||AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode());
        int res = 0;
        if(flag){
            //预授权流水表更新
            res = preAuthorizationLogUpdate(authRecordedDTO);
        }
        //普通交易和撤销交易记录预授权流水
        if(AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                ||AuthTransTypeEnum.NORMAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())){
            res = cupAddPreAuthLog(authRecordedDTO,accountManagementInfoDTO,organizationInfoResDTO,authorProcessInfo);
        }
        return res;
    }

    /**
     * 预授权流水表update
     * 预授权请求交易
     * @return int
     */
    @BatchSharedAnnotation
    private int preAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO){
        int res = 0;
        //本交易为撤销交易(authTransactionTypeCode为2)
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            res = preRvocationTrans(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        }
        //本交易为冲正交易(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            res = preReversalTrans(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            res = preRevocationReversal(authRecordedDTO,authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        }
        return res;
    }

    /**
     * 预授权完成处理
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @param preAuthorizationLogDTO {@link PreAuthorizationLogDTO}
     * @return  影响行数
     */
    @BatchSharedAnnotation
    public int modifyPreFinishAuthorizationLog(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,PreAuthorizationLogDTO preAuthorizationLogDTO) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        AccountManagementInfoDTO accountManagementInfoDTO = authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
        OrganizationInfoResDTO organizationInfoResDTO =authorizationCheckProcessingPayload.getOrgInfo();
        AuthorisationProcessingResDTO authorProcessInfo =authorizationCheckProcessingPayload.getAuthorProcessInfo();
        //预授权完成 四种交易都要更新预授权流水
        int res = preFinishAuthorizationLogUpdate(authRecordedDTO,preAuthorizationLogDTO);
        //普通交易和撤销交易记录预授权流水
        if(AuthTransTypeEnum.NORMAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                ||AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())){
            res = cupAddPreAuthLog(authRecordedDTO,accountManagementInfoDTO,organizationInfoResDTO,authorProcessInfo);
        }
        return res;
    }



    /**
     * 预授权完成更新原预授权请求信息
     * @param authRecordedDTO  AuthRecordedDTO
     * @param preAuthorizationLogDTO  PreAuthorizationLogDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int preFinishNormalTrans(PreAuthorizationLogDTO preAuthorizationLogDTO,AuthRecordedDTO authRecordedDTO){
        //1.更新预授权请求状态
        preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.COMPLETED.getCode());
        preAuthorizationLogDTO.setTransactionCompleteAmount(authRecordedDTO.getAuthTransactionAmount());
        preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
            return 1;
        } else {
            return preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);
        }
    }

    /**
     * 预授权完成撤销
     * 更新原预授权请求信息
     * @param preAuthorizationLogDTO  PreAuthorizationLogDTO
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int preFinishRvocationTrans(PreAuthorizationLogDTO preAuthorizationLogDTO,AuthRecordedDTO authRecordedDTO){
        //1.更新原预授权请求状态
        preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.INIT.getCode());
        preAuthorizationLogDTO.setTransactionCompleteAmount(authRecordedDTO.getAuthTransactionAmount());
        preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);
        }

        //2.更新原预授权完成交易
        PreAuthorizationLogDTO preAuthFinishAuthorizationLogDTO = preAuthorizationLogService
                .getByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkPreAuthorizationLog(preAuthFinishAuthorizationLogDTO);
        }
        preAuthFinishAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.REVOCATIONED.getCode());
        preAuthFinishAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthFinishAuthorizationLogDTO.setStatusChageDate(LocalDate.now());

        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthFinishAuthorizationLogDTO, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthFinishAuthorizationLogDTO);
        }

        return  AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 预授权完成撤销冲正
     * @param preAuthorizationLogDTO PreAuthorizationLogDTO
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int preFinishRevocationReversal(PreAuthorizationLogDTO preAuthorizationLogDTO,AuthRecordedDTO authRecordedDTO){

        //1.获取到撤销交易的交易流水
        PreAuthorizationLogDTO preOrignRevocationLogDto = preAuthorizationLogService
                .getByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkPreAuthorizationLog(preOrignRevocationLogDto);
        }
        preOrignRevocationLogDto.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.REVERSALED.getCode());
        preOrignRevocationLogDto.setTransactionCompleteAmount(authRecordedDTO.getAuthTransactionAmount());
        preOrignRevocationLogDto.setUpdateTime(LocalDateTime.now());
        preOrignRevocationLogDto.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preOrignRevocationLogDto, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preOrignRevocationLogDto);
        }

        //2.获取到撤销交易的原交易流水
        PreAuthorizationLogDTO preAuthFinishAuthorizationLogDTO = preAuthorizationLogService
                .getByGlobalFlowNumber(preOrignRevocationLogDto.getOriginalGlobalFlowNumber());

        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkPreAuthorizationLog(preAuthFinishAuthorizationLogDTO);
        }
        preAuthFinishAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.INIT.getCode());
        preAuthFinishAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthFinishAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthFinishAuthorizationLogDTO, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthFinishAuthorizationLogDTO);
        }

        //3.最原始交易的交易流水
        preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.COMPLETED.getCode());
        preAuthorizationLogDTO.setTransactionCompleteAmount(authRecordedDTO.getAuthTransactionAmount());
        preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);
        }

        return  AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 预授权完成冲正
     * 更新原预授权请求信息
     * @param preAuthorizationLogDTO  PreAuthorizationLogDTO
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int preFinishReversalTrans(PreAuthorizationLogDTO preAuthorizationLogDTO,AuthRecordedDTO authRecordedDTO){
        //1.更新原预授权请求状态
        preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.INIT.getCode());
        preAuthorizationLogDTO.setTransactionCompleteAmount(authRecordedDTO.getAuthTransactionAmount());
        preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);
        }
        //2.更新原预授权完成交易
        PreAuthorizationLogDTO preAuthFinishAuthorizationLogDTO = preAuthorizationLogService
                .getByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkPreAuthorizationLog(preAuthFinishAuthorizationLogDTO);
        }
        preAuthFinishAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.REVERSALED.getCode());
        preAuthFinishAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthFinishAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthFinishAuthorizationLogDTO, true);
        } else {
            preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthFinishAuthorizationLogDTO);
        }
        return  AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 预授权-撤销交易
     * @param globalFlowNumber globalFlowNumber
     * @return int
     */
    @BatchSharedAnnotation
    private int preRvocationTrans(String globalFlowNumber){
        PreAuthorizationLogDTO preAuthorizationLogDTO = getPreAuthorizationLogByGlobalFlowNumber(globalFlowNumber);
        if (preAuthorizationLogDTO != null) {
            preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.REVOCATIONED.getCode());
            preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
            preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
            if(CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            } else {
                int res = preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);
                if(res != AuthConstans.ONE ){
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.PA_PU);
                }
            }
            logger.error("pre evocationTrans AuthorizationLog update error!");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        logger.error("original pre authorizationLog cannot be found!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 预授权-冲正交易
     * @param globalFlowNumber 全局流水号
     * @return int
     */
    @BatchSharedAnnotation
    private int preReversalTrans(String globalFlowNumber){
        PreAuthorizationLogDTO preAuthorizationLogDTO = getPreAuthorizationLogByGlobalFlowNumber(globalFlowNumber);
        if (preAuthorizationLogDTO != null) {
            preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.REVERSALED.getCode());
            preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
            preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
            if(CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
                return 1;
            } else {
                int res = preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);

                if(res != AuthConstans.ONE ){
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.PA_PU);
                }
                return res;
            }
        }
        logger.error("original pre authorizationLog cannot be found!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 预授权-撤销的冲正交易
     *
     * @param authRecordedDTO
     * @param originalGlobalFlowNumber  originalGlobalFlowNumber
     * @return int
     */
    @BatchSharedAnnotation
    private int preRevocationReversal(AuthRecordedDTO authRecordedDTO, String originalGlobalFlowNumber){
        //撤销交易的预授权流水
        PreAuthorizationLogDTO preAuthorizationLogDTO = getPreAuthorizationLogByGlobalFlowNumber(originalGlobalFlowNumber);
        if(preAuthorizationLogDTO == null){
            logger.error("authorizationLogDTO cannot be found！");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.REVERSALED.getCode());
        preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        if(CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
            return 1;
        } else {
            int res = preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(preAuthorizationLogDTO);
            if(res != AuthConstans.ONE){
                logger.error("preRevocationReversal failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.PA_PU);
            }
        }
        //撤销交易的原交易的授权流水
        PreAuthorizationLogDTO orignalPreAuthorizationLogDTO = getPreAuthorizationLogByGlobalFlowNumber(
                preAuthorizationLogDTO.getOriginalGlobalFlowNumber());
        if(orignalPreAuthorizationLogDTO == null){
            logger.error("orignalPreAuthorizationLogDTO cannot be found！");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        orignalPreAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.INIT.getCode());
        orignalPreAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        orignalPreAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        orignalPreAuthorizationLogDTO.setLimitUnitJson(JacksonUtils.toJsonStr(authRecordedDTO.getLimitUnitList()));
        orignalPreAuthorizationLogDTO.setTransCtrlUnitId(authRecordedDTO.getTransCtrlUnitId());
        if(CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, true);
            return 1;
        } else {
            int res = preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(orignalPreAuthorizationLogDTO);
            if(res != AuthConstans.ONE ){
                logger.error("preRevocationReversal failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.PA_PU);
            }
            return res;
        }
    }

    /**
     * 预授权流水add
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int cupAddPreAuthLog(AuthRecordedDTO authRecordedDTO,AccountManagementInfoDTO accountManagementInfoDTO,OrganizationInfoResDTO organizationInfoResDTO,AuthorisationProcessingResDTO authorProcessInfo){
        PreAuthorizationLogDTO preAuthorizationLogDTO = new PreAuthorizationLogDTO();
        preAuthorizationLogDTO.setPreauthLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        preAuthorizationLogDTO.setPreauthLogTime(authRecordedDTO.getAuthTransmissionTime());
        preAuthorizationLogDTO.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        preAuthorizationLogDTO.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        preAuthorizationLogDTO.setCardNumber(authRecordedDTO.getAuthCardNumber());
        preAuthorizationLogDTO.setAccountManagementId(accountManagementInfoDTO.getAccountManagementId());
        preAuthorizationLogDTO.setPreauthTxnType(authRecordedDTO.getPreauthTxnType());
        preAuthorizationLogDTO.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        preAuthorizationLogDTO.setTransactionCompleteAmount(authRecordedDTO.getTransactionAmountComplete());
        preAuthorizationLogDTO.setCardholderBillingRate(authRecordedDTO.getAuthCardholderBillingRate());
        preAuthorizationLogDTO.setPreauthForwardingNumber(authRecordedDTO.getAuthForwardingIdentificationCode());
        preAuthorizationLogDTO.setCardAcceptorIdentification(authRecordedDTO.getAuthCardAcceptorIdentification());
        preAuthorizationLogDTO.setPreauthAcqNumber(authRecordedDTO.getAuthAcquiringIdentificationCode());
        preAuthorizationLogDTO.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.INIT.getCode());
        preAuthorizationLogDTO.setCreateTime(LocalDateTime.now());
        preAuthorizationLogDTO.setUpdateTime(LocalDateTime.now());
        preAuthorizationLogDTO.setPreauthSystemTrace(authRecordedDTO.getAuthSystemTraceAuditNumber());
        preAuthorizationLogDTO.setCreditLimitNodeId(authRecordedDTO.getAuthCreditLimitNodeId());
        preAuthorizationLogDTO.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        preAuthorizationLogDTO.setPreauthCode(authRecordedDTO.getAuthAuthCode());
        preAuthorizationLogDTO.setStatusChageDate(LocalDate.now());
        preAuthorizationLogDTO.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        preAuthorizationLogDTO.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        preAuthorizationLogDTO.setCustomerId(authRecordedDTO.getAuthCustomerId());
        preAuthorizationLogDTO.setOrganizationNumber(organizationInfoResDTO.getOrganizationNumber());
        LocalDate releaseDate = organizationInfoResDTO.getNextProcessingDay().plusDays(authorProcessInfo.getAuthorisationRemainDays());
        preAuthorizationLogDTO.setTransactionReleaseDate(releaseDate);
        preAuthorizationLogDTO.setLimitUnitJson(JacksonUtils.toJsonStr(authRecordedDTO.getLimitUnitList()));
        preAuthorizationLogDTO.setTransCtrlUnitId(authRecordedDTO.getTransCtrlUnitId());
        preAuthorizationLogDTO.setLimitUnitVersion(authRecordedDTO.getLimitUnitVersion());
        preAuthorizationLogDTO.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        preAuthorizationLogDTO.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
        if (CustAccountBO.isBatch()) {
            if (preAuthorizationLogDTO.getPreauthLogId() != null){
                preAuthorizationLogDTO.setPartitionKey(PartitionKeyUtils.partitionKey(preAuthorizationLogDTO.getCustomerId()));
            }
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updatePreAuthorizationLog(preAuthorizationLogDTO, false);
            return 1;
        } else {
            return preAuthorizationLogService.insert(preAuthorizationLogDTO, accountManagementInfoDTO);
        }
    }

    /**
     * cup银联 授权流水数据更新
     * @param authRecordedDTO AuthRecordedDTO
     * @return int
     */
    @BatchSharedAnnotation
    private int cupAddAuthLog(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogDTO authorizationLogDTO = new AuthorizationLogDTO();
        authorizationLogDTO.setAuthLogId(Optional.ofNullable(authRecordedDTO.getCurrentAuthLogId()).orElse(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        authorizationLogDTO.setAuthLogTime(LocalDateTime.now());
        //撤销冲正交易，GlobalFlowNumber和OriginalGlobalFlowNumber的修改
        //原交易取撤销交易的GlobalFlowNumber，GlobalFlowNumber重新生成
        if(Objects.equals(authRecordedDTO.getAuthTransactionTypeCode(),AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode())){
            authorizationLogDTO.setGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            authorizationLogDTO.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        }else{
            authorizationLogDTO.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
            authorizationLogDTO.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        }
        authorizationLogDTO.setMessageTypeId(authRecordedDTO.getAuthMessageTypeId());
        authorizationLogDTO.setCardNumber(authRecordedDTO.getAuthCardNumber());
        authorizationLogDTO.setProcessingCode(authRecordedDTO.getAuthProcessingCode());
        authorizationLogDTO.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        authorizationLogDTO.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        authorizationLogDTO.setTransmissionTime(authRecordedDTO.getAuthTransmissionTime());
        authorizationLogDTO.setCardholderBillingRate(authRecordedDTO.getAuthCardholderBillingRate()==null
                ? null:authRecordedDTO.getAuthCardholderBillingRate().intValue());
        authorizationLogDTO.setSystemTraceAuditNumber(authRecordedDTO.getAuthSystemTraceAuditNumber());
        authorizationLogDTO.setLocalTransactionDate(authRecordedDTO.getAuthLocalTransactionDate());
        authorizationLogDTO.setLocalTransactionTime(authRecordedDTO.getAuthLocalTransactionTime());
        authorizationLogDTO.setCardExpirationDate(authRecordedDTO.getAuthCardExpirationDate());
        authorizationLogDTO.setSettlementDate(authRecordedDTO.getAuthSettlementDate());
        authorizationLogDTO.setMerchantType(authRecordedDTO.getAuthMerchantType());
        authorizationLogDTO.setMerchantCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        authorizationLogDTO.setServicePointCardCode(authRecordedDTO.getAuthServicePointCardCode());
        authorizationLogDTO.setServicePointPinCode(authRecordedDTO.getAuthServicePointPinCode());
        authorizationLogDTO.setCardSequenceNumber(authRecordedDTO.getAuthCardSequenceNumber());
        authorizationLogDTO.setServicePointConditionCode(authRecordedDTO.getAuthServicePointConditionCode());
        authorizationLogDTO.setTransactionFeeIndicator(authRecordedDTO.getAuthTransactionFeeIndicator());
        authorizationLogDTO.setTransactionFee(authRecordedDTO.getAuthTransactionFee());
        authorizationLogDTO.setAcquiringIdentificationCode(authRecordedDTO.getAuthAcquiringIdentificationCode());
        authorizationLogDTO.setForwardingIdentificationCode(authRecordedDTO.getAuthForwardingIdentificationCode());
//        authorizationLogDTO.setTrack2Data(authRecordedDTO.getAuthTrack2Data());
        authorizationLogDTO.setRetrievalReferenceNumber(authRecordedDTO.getAuthRetrievalReferenceNumber());
        authorizationLogDTO.setAuthIdentificationResponse(authRecordedDTO.getAuthAuthIdentificationResponse());
        authorizationLogDTO.setResponseCode(authRecordedDTO.getAuthResponseCode());
        authorizationLogDTO.setCardAcceptorTerminalCode(authRecordedDTO.getAuthCardAcceptorTerminalCode());
        authorizationLogDTO.setCardAcceptorIdentification(authRecordedDTO.getAuthCardAcceptorIdentification());
        authorizationLogDTO.setCardAcceptorNameLocation(authRecordedDTO.getAuthCardAcceptorNameLocation());
//        authorizationLogDTO.setTrack1Data(authRecordedDTO.getAuthTrack1Data());
        authorizationLogDTO.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        authorizationLogDTO.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
//        authorizationLogDTO.setPinData(authRecordedDTO.getAuthPinData());
        authorizationLogDTO.setIccSystemRelatedData(authRecordedDTO.getAuthIccSystemRelatedData());
        authorizationLogDTO.setTerminalType(authRecordedDTO.getAuthTerminalType());
        authorizationLogDTO.setNoCardVerificationValue(authRecordedDTO.getAuthNoCardVerificationValue());
        authorizationLogDTO.setOriginalMessageTypeId(authRecordedDTO.getAuthOriginalMessageTypeId());
        authorizationLogDTO.setOriginalSystemTraceNumber(authRecordedDTO.getAuthOriginalSystemTraceAuditNumber());
        authorizationLogDTO.setOriginalTransmissionTime(authRecordedDTO.getAuthOriginalTransmissionTime());
        authorizationLogDTO.setOriginalAcquiringIdeCode(authRecordedDTO.getAuthOriginalAcquiringIdentificationCode());
        authorizationLogDTO.setOriginalForwardingIdeCode(authRecordedDTO.getAuthOriginalForwardingIdentificationCode());
        authorizationLogDTO.setAuthTypeCode(authRecordedDTO.getAuthAuthTypeCode());
        //visa 判断处理
        if (AuthTransTypeTopCodeEnum.AUTHORIZATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode())) {
            if (TranTypeDetailEnum.VISA_CONSUMPTION.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
                authorizationLogDTO.setTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
            }
            if (TranTypeDetailEnum.VISA_CASH.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
                authorizationLogDTO.setTransactionTypeTopCode(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode());
            }
        }else {
            authorizationLogDTO.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        }
        authorizationLogDTO.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        authorizationLogDTO.setAuthCode(authRecordedDTO.getAuthAuthCode());
        authorizationLogDTO.setErrorCode(authRecordedDTO.getErrorDetailCode());
        authorizationLogDTO.setCardActivationResultCode(authRecordedDTO.getAuthCardActivationResultCode());
        authorizationLogDTO.setCardStatusResultCode(authRecordedDTO.getAuthCardStatusResultCode());
        authorizationLogDTO.setCardExpireDateResultCode(authRecordedDTO.getAuthCardExpireDateResultCode());
        authorizationLogDTO.setCardLimitResultCode(authRecordedDTO.getAuthCardLimitResultCode());
        authorizationLogDTO.setCardBlockResultCode(authRecordedDTO.getAuthCardBlockResultCode());
        authorizationLogDTO.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogDTO.setCustomerBlockResultCode(authRecordedDTO.getAuthCustomerBlockResultCode());
        authorizationLogDTO.setFraudFileResultCode(authRecordedDTO.getAuthFraudFileResultCode());
        authorizationLogDTO.setTerminalBlacklistResultCode(authRecordedDTO.getAuthTerminalBlacklistResultCode());
        authorizationLogDTO.setCvvResultCode(authRecordedDTO.getAuthCvvResultCode());
        authorizationLogDTO.setAvailLimitResultCode(authRecordedDTO.getAuthAvailLimitResultCode());
        authorizationLogDTO.setVelocityCheckResultCode(authRecordedDTO.getAuthVelocityCheckResultCode());
        authorizationLogDTO.setUsageCardResultCode(authRecordedDTO.getAuthUsageCardResultCode());
        authorizationLogDTO.setSingleLimitResultCode(authRecordedDTO.getAuthSingleLimitResultCode());
        authorizationLogDTO.setMaxApproveNmbrResultCode(authRecordedDTO.getAuthMaxApproveNmbrResultCode());
        authorizationLogDTO.setMaxDeclineNmbrResultCode(authRecordedDTO.getAuthMaxDeclineNmbrResultCode());
        authorizationLogDTO.setFallbackResultCode(authRecordedDTO.getAuthFallbackResultCode());
        //中间变量STATUS
        authorizationLogDTO.setTrancactionStatus(authRecordedDTO.getAuthTrancactionStatus());
        //中间变量TYPE
        authorizationLogDTO.setReversalType(authRecordedDTO.getAuthReversalType());
        authorizationLogDTO.setAuthTransactionSourceCode(authRecordedDTO.getAuthTransactionSourceCode());
        //《授权额度更新》处理后获取的limit_type_num
        authorizationLogDTO.setCreditLimitNodeId(authRecordedDTO.getAuthCreditLimitNodeId());
        authorizationLogDTO.setCustomerId(authRecordedDTO.getAuthCustomerId());
        authorizationLogDTO.setTransactionTypeCode(authRecordedDTO.getAuthTransactionTypeCode());
        if (StringUtils.isNotEmpty(authRecordedDTO.getAuthCustomerId())) {
            authorizationLogDTO.setPartitionKey(PartitionKeyUtils.StrPartitionKey(authRecordedDTO.getAuthCustomerId()));
        } else {
            authorizationLogDTO.setPartitionKey("");
        }
        authorizationLogDTO.setCreateTime(LocalDateTime.now());
        authorizationLogDTO.setUpdateTime(LocalDateTime.now());
        authorizationLogDTO.setUpdateBy(Constant.DEFAULT_USER);
        authorizationLogDTO.setVersionNumber(1L);
        //新增字段
        authorizationLogDTO.setInteractiveModeFlag(authRecordedDTO.getAuthInteractiveModeFlag());
        authorizationLogDTO.setTransactionMedia(authRecordedDTO.getAuthTransactionMedia());
        authorizationLogDTO.setIdType(authRecordedDTO.getAuthIdType());
        authorizationLogDTO.setIdNumber(authRecordedDTO.getAuthIdNumber());
        authorizationLogDTO.setFormatId(authRecordedDTO.getAuthFormatId());
        authorizationLogDTO.setName(authRecordedDTO.getAuthName());
        authorizationLogDTO.setMobileNo(authRecordedDTO.getAuthMobileNo());
        authorizationLogDTO.setAccountStatusResultCode(authRecordedDTO.getAuthAccountStatusResultCode());
        authorizationLogDTO.setCustomerStatusResultCode(authRecordedDTO.getAuthCustomerStatusResultCode());
        authorizationLogDTO.setPinBlockResultCode(authRecordedDTO.getAuthPinBlockResultCode());
        authorizationLogDTO.setCvv2ResultCode(authRecordedDTO.getAuthCvv2ResultCode());
        authorizationLogDTO.setFisrtUsageResultCode(authRecordedDTO.getAuthFisrtUsageResultCode());
        authorizationLogDTO.setIdTypeResultCode(authRecordedDTO.getAuthIdTypeResultCode());
        authorizationLogDTO.setIdNumberResultCode(authRecordedDTO.getAuthIdNumberResultCode());
        authorizationLogDTO.setNameResultCode(authRecordedDTO.getAuthNameResultCode());
        authorizationLogDTO.setMobileNoResultCode(authRecordedDTO.getAuthMobileNoResultCode());
        authorizationLogDTO.setInternalSpecialResultCode(authRecordedDTO.getAuthInnerSpecialResultCode());
        authorizationLogDTO.setInternetSwitchResultCode(authRecordedDTO.getAuthInternetTransactionResultCode());
        authorizationLogDTO.setInternalCashSwResultCode(authRecordedDTO.getAuthInternalCashResultCode());
        authorizationLogDTO.setExternalCashSwResultCode(authRecordedDTO.getAuthExternalCashResultCode());
        authorizationLogDTO.setExternalSwitchResultCode(authRecordedDTO.getAuthExternalTransResultCode());
        authorizationLogDTO.setAtmSwitchResultCode(authRecordedDTO.getAuthAtmSwitchResultCode());
        authorizationLogDTO.setExistCardSwitchResultCode(authRecordedDTO.getAuthExistCardSwitchResultCode());
        authorizationLogDTO.setNoCardSwitchResultCode(authRecordedDTO.getAuthNoCardSwitchResultCode());
        authorizationLogDTO.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogDTO.setAdditionalAmounts(authRecordedDTO.getAuthAdditionalAmounts());
        //实时入账交易码,实时入账交易码(冲正和撤销用)
        authorizationLogDTO.setPostingTransactionCode(authRecordedDTO.getPostingTransactionCode());
        authorizationLogDTO.setPostingTransactionCodeRev(authRecordedDTO.getPostingTransactionCodeRev());
        authorizationLogDTO.setMerchantFraudResultCode(authRecordedDTO.getMerchantFraudResultCode());
        authorizationLogDTO.setCardSafetyLockResultCode(authRecordedDTO.getAuthCardSecuritySwitchResultCode());
        authorizationLogDTO.setAuthForeignMagneticStripeResultCode(authRecordedDTO.getAuthForeignMagneticStripeResultCode());
        authorizationLogDTO.setOpponentAccountName(authRecordedDTO.getOpponentAccountName());
        authorizationLogDTO.setOpponentAccountNumber(authRecordedDTO.getOpponentAccountNumber());
        authorizationLogDTO.setOpponentBankId(authRecordedDTO.getOpponentBankId());
        authorizationLogDTO.setOpponentTxnArea(authRecordedDTO.getOpponentTxnArea());
        authorizationLogDTO.setMerchantInfo2ndCode(authRecordedDTO.getMerchantInfo2ndCode());
        authorizationLogDTO.setMerchantInfo2ndName(authRecordedDTO.getMerchantInfo2ndName());
        authorizationLogDTO.setOpponentTxnArea1(authRecordedDTO.getOpponentTxnArea1());
        authorizationLogDTO.setOpponentTxnArea2(authRecordedDTO.getOpponentTxnArea2());
        authorizationLogDTO.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        authorizationLogDTO.setAnyCloudFraudResultCode(authRecordedDTO.getAuthAnyCloudFraudResultCode());
        authorizationLogDTO.setSenderAccount(authRecordedDTO.getSenderAccount());
        authorizationLogDTO.setSenderName(authRecordedDTO.getSenderName());
        authorizationLogDTO.setSenderAccountBankNumber(authRecordedDTO.getSenderAccountBankNumber());
        authorizationLogDTO.setSendingOrganizationChineseAbbreviation(authRecordedDTO.getSendingOrganizationChineseAbbreviation());
        authorizationLogDTO.setTurnOutPartyCardNumber(authRecordedDTO.getTurnOutPartyCardNumber());
        authorizationLogDTO.setTransferPartyCardNumber(authRecordedDTO.getTransferPartyCardNumber());
        authorizationLogDTO.setTransfereeName(authRecordedDTO.getTransfereeName());
        authorizationLogDTO.setTurnOutPartyName(authRecordedDTO.getTurnOutPartyName());
        authorizationLogDTO.setClientIpAddress(authRecordedDTO.getClientIpAddress());
        authorizationLogDTO.setMacAddress(authRecordedDTO.getMacAddress());
        authorizationLogDTO.setMerchantInfo2ndSort(authRecordedDTO.getMerchantInfo2ndSort());
        authorizationLogDTO.setTradingAddress(authRecordedDTO.getTradingAddress());
        // add 2022-12-19
        OrganizationInfoResDTO orgInfo = organizationInfoService.findOrganizationInfo(authRecordedDTO.getOrganizationNumber());
        LocalDate nextProcessingDay = orgInfo.getNextProcessingDay();
        String f15 = authRecordedDTO.getAuthSettlementDate();
        if (StringUtils.isNotBlank(f15) && f15.length() == 4) {
            if (f15.startsWith("01") && nextProcessingDay.getMonthValue() == 12) {
                nextProcessingDay = LocalDate.of(nextProcessingDay.getYear()+1, Integer.parseInt(f15.substring(0,2)), Integer.parseInt(f15.substring(2,4)));
            } else {
                nextProcessingDay = LocalDate.of(nextProcessingDay.getYear(), Integer.parseInt(f15.substring(0,2)), Integer.parseInt(f15.substring(2,4)));
            }
        }
        authorizationLogDTO.setProcessDate(nextProcessingDay);

        authorizationLogDTO.setCustomizedInstallmentInfo(authRecordedDTO.getAuthReconciliationTotals());
        authorizationLogDTO.setLocalUsedResultCode(authRecordedDTO.getLocalUsedResultCode());
        authorizationLogDTO.setCountryMccResultCode(authRecordedDTO.getCountryMccResultCode());
        authorizationLogDTO.setArqcCheckResultCode(authRecordedDTO.getArqcCheckResultCode());
        authorizationLogDTO.setMccResultCode(authRecordedDTO.getAuthMccResultCode());
        authorizationLogDTO.setNetworkReferenceId(authRecordedDTO.getAuthNetworkReferenceId());
        authorizationLogDTO.setAdditionalAuthenticationData(authRecordedDTO.getAuthAdditionalAuthenticationData());
        authorizationLogDTO.setCavvResultCode(authRecordedDTO.getCavvResultCode());
        authorizationLogDTO.setEcommerceIndicator(authRecordedDTO.getElectronicCommerceIndicators());
        authorizationLogDTO.setDsTransactionId(authRecordedDTO.getDirectoryServerTransactionId());
        authorizationLogDTO.setPrivateData(authRecordedDTO.getAuthTransSpecificData());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(BeanMapping.copy(authorizationLogDTO, AuthorizationLogDTO.class), false);
            return 1;
        } else {
            return authorizationLogService.insert(authorizationLogDTO);
        }
    }

    /**
     * visa 授权流水数据更新
     */
    @BatchSharedAnnotation
    private int visaAddAuthLog(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogVisa authorizationLogVisa = new AuthorizationLogVisa();
        authorizationLogVisa.setAuthLogId(Optional.ofNullable(authRecordedDTO.getCurrentAuthLogId()).orElse(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        authorizationLogVisa.setAuthLogTime(LocalDateTime.now());
        authorizationLogVisa.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        authorizationLogVisa.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        authorizationLogVisa.setMessageTypeId(authRecordedDTO.getAuthMessageTypeId());
        authorizationLogVisa.setCardNumber(authRecordedDTO.getAuthCardNumber());
        authorizationLogVisa.setProcessingCode(authRecordedDTO.getAuthProcessingCode());
        //部分撤销使用到的金额
        authorizationLogVisa.setReplaceActualAmount(authRecordedDTO.getAuthReplaceAmountActualVisa());
        authorizationLogVisa.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        authorizationLogVisa.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        authorizationLogVisa.setTransmissionTime(authRecordedDTO.getAuthTransmissionTime());
        authorizationLogVisa.setCardholderBillingRate(authRecordedDTO.getAuthCardholderBillingRate().intValue());
        authorizationLogVisa.setSystemTraceAuditNumber(authRecordedDTO.getAuthSystemTraceAuditNumber());
        authorizationLogVisa.setLocalTransactionTime(authRecordedDTO.getAuthLocalTransactionTime());
        authorizationLogVisa.setLocalTransactionDate(authRecordedDTO.getAuthLocalTransactionDate());
        authorizationLogVisa.setCardExpirationDate(authRecordedDTO.getAuthCardExpirationDate());
        authorizationLogVisa.setSettlementDate(authRecordedDTO.getAuthSettlementDate());
        authorizationLogVisa.setMerchantType(authRecordedDTO.getAuthMerchantType());
        authorizationLogVisa.setMerchantCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        authorizationLogVisa.setServicePointEntryModeCode(authRecordedDTO.getAuthServicePointEntryModeCode());
        authorizationLogVisa.setCardSequenceNumber(authRecordedDTO.getAuthCardSequenceNumber());
        authorizationLogVisa.setServicePointConditionCode(authRecordedDTO.getAuthServicePointConditionCode());
        authorizationLogVisa.setServicePointPinCode(authRecordedDTO.getAuthServicePointPinCaptureCode());
        //authorizationLogVisa.setTransactionFee(authRecordedDTO.getAuthTransactionFee())
        authorizationLogVisa.setAcquiringIdentificationCode(authRecordedDTO.getAuthAcquiringIdentificationCode());
        authorizationLogVisa.setForwardingIdentificationCode(authRecordedDTO.getAuthForwardingIdentificationCode());
        authorizationLogVisa.setTrack2Data(authRecordedDTO.getAuthTrack2Data());
        authorizationLogVisa.setRetrievalReferenceNumber(authRecordedDTO.getAuthRetrievalReferenceNumber());
        authorizationLogVisa.setAuthIdentificationResponse(authRecordedDTO.getAuthAuthIdentificationResponse());
        authorizationLogVisa.setResponseCode(authRecordedDTO.getAuthResponseCode());
        authorizationLogVisa.setCardAcceptorTerminalCode(authRecordedDTO.getAuthCardAcceptorTerminalCode());
        authorizationLogVisa.setCardAcceptorIdentification(authRecordedDTO.getAuthCardAcceptorIdentification());
        authorizationLogVisa.setCardAcceptorNameLocation(authRecordedDTO.getAuthCardAcceptorNameLocation());
        authorizationLogVisa.setAdditionalResponseData(authRecordedDTO.getAuthAdditionalResponseData());
        authorizationLogVisa.setTrack1Data(authRecordedDTO.getAuthTrack1Data());
        authorizationLogVisa.setAdditionalDataPrivate(authRecordedDTO.getAuthAdditionalDataPrivate());
        authorizationLogVisa.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        authorizationLogVisa.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
        authorizationLogVisa.setPinData(authRecordedDTO.getAuthPinData());
        authorizationLogVisa.setSecurityRelatedControlInfo(authRecordedDTO.getAuthSecurityRelatedControlInformation());
        authorizationLogVisa.setAdditionalAmounts(authRecordedDTO.getAuthAdditionalAmounts());
        authorizationLogVisa.setIccSystemRelatedData(authRecordedDTO.getAuthIccSystemRelatedData());
        authorizationLogVisa.setNationalPointServiceGeog1(authRecordedDTO.getAuthNationalPointOfServiceGeographicData1());
        authorizationLogVisa.setAdditionalPosInformation(authRecordedDTO.getAuthAdditionalPosInformation());
        authorizationLogVisa.setOtherAmount(authRecordedDTO.getAuthOtherAmount());
        authorizationLogVisa.setCustPaymentServiceFields(authRecordedDTO.getAuthCustomerPaymentServiceFields());
        authorizationLogVisa.setVipPrivateUseField(authRecordedDTO.getAuthVipPrivateUseField());
        authorizationLogVisa.setRecInstitutionCountryCode(authRecordedDTO.getAuthReceivingInstitutionCountryCode());
        authorizationLogVisa.setNetworkManagementInfoCode(authRecordedDTO.getAuthNetworkManagementInformationCode());
        authorizationLogVisa.setActionData(authRecordedDTO.getAuthActionData());
        authorizationLogVisa.setOriginalMessageTypeId(authRecordedDTO.getAuthOriginalMessageTypeId());
        authorizationLogVisa.setOriginalSystemTraceNumber(authRecordedDTO.getAuthOriginalSystemTraceAuditNumber());
        authorizationLogVisa.setOriginalTransmissionTime(authRecordedDTO.getAuthOriginalTransmissionTime());
        authorizationLogVisa.setOriginalAcquiringIdeCode(authRecordedDTO.getAuthOriginalAcquiringIdentificationCode());
        authorizationLogVisa.setOriginalForwardingIdeCode(authRecordedDTO.getAuthOriginalForwardingIdentificationCode());
        //authorizationLogVisa.setFileUpdateCode(authRecordedDTO.getAuthFileUpdateCode())
        authorizationLogVisa.setFileSecurityCode(authRecordedDTO.getAuthFileSecurityCode());
        authorizationLogVisa.setReplacementAmounts(authRecordedDTO.getAuthReplacementAmounts());
        authorizationLogVisa.setRecInstitutionIdentifyCode(authRecordedDTO.getAuthReceivingInstitutionIdentificationCode());
        authorizationLogVisa.setFileName(authRecordedDTO.getAuthFileName());
        authorizationLogVisa.setAccountIdentification1(authRecordedDTO.getAuthAccountIdentification1());
        authorizationLogVisa.setAccountIdentification2(authRecordedDTO.getAuthAccountIdentification2());
        authorizationLogVisa.setTransactionDescription5(authRecordedDTO.getAuthTransactionDescription5());
        authorizationLogVisa.setAdditionalTraceData(authRecordedDTO.getAuthAdditionalTraceData());
        authorizationLogVisa.setCardIssuerReferenceData(authRecordedDTO.getAuthCardIssuerReferenceData());
        authorizationLogVisa.setNationalUse(authRecordedDTO.getAuthNationalUse());
        authorizationLogVisa.setIntraCountryData(authRecordedDTO.getAuthIntraCountryData());
        authorizationLogVisa.setIssInstitutionIdentifyCode(authRecordedDTO.getAuthIssuingInstitutionIdentificationCode());
        authorizationLogVisa.setVerificationData(authRecordedDTO.getAuthVerificationData());
        authorizationLogVisa.setSupportingInformation(authRecordedDTO.getAuthSupportingInformation());
        authorizationLogVisa.setVisaPrivateUseFields(authRecordedDTO.getAuthVisaPrivateUseFields());
        authorizationLogVisa.setTerminalCapabilityProfile(authRecordedDTO.getAuthTerminalCapabilityProfile());
        authorizationLogVisa.setTerminalVerificationResults(authRecordedDTO.getAuthTerminalVerificationResults());
        authorizationLogVisa.setUnpredictableNumber(authRecordedDTO.getAuthUnpredictableNumber());
        authorizationLogVisa.setTerminalSerialNumber(authRecordedDTO.getAuthTerminalSerialNumber());
        authorizationLogVisa.setVisaDiscretionaryData(authRecordedDTO.getAuthVisaDiscretionaryData());
        authorizationLogVisa.setIssuerDiscretionaryData(authRecordedDTO.getAuthIssuerDiscretionaryData());
        authorizationLogVisa.setCryptogram(authRecordedDTO.getAuthCryptogram());
        authorizationLogVisa.setApplicationTransactionCnt(authRecordedDTO.getAuthApplicationTransactionCounter());
        authorizationLogVisa.setApplicationInterchgProfile(authRecordedDTO.getAuthApplicationInterchangeProfile());
        authorizationLogVisa.setArpcCryptogramAndCode(authRecordedDTO.getAuthArpcResponseCryptogramAndCode());
        authorizationLogVisa.setIssuerAuthenticationData(authRecordedDTO.getAuthIssuerAuthenticationData());
        authorizationLogVisa.setIssuerScript(authRecordedDTO.getAuthIssuerScript());
        authorizationLogVisa.setIssuerScriptResults(authRecordedDTO.getAuthIssuerScriptResults());
        authorizationLogVisa.setCryptogramTransactionType(authRecordedDTO.getAuthCryptogramTransactionType());
        authorizationLogVisa.setTerminalCountryCode(authRecordedDTO.getAuthTerminalCountryCode());
        authorizationLogVisa.setTerminalTransactionDate(authRecordedDTO.getAuthTerminalTransactionDate());
        authorizationLogVisa.setCryptogramAmount(authRecordedDTO.getAuthCryptogramAmount());
        authorizationLogVisa.setCryptogramCurrencyCode(authRecordedDTO.getAuthCryptogramCurrencyCode());
        authorizationLogVisa.setCryptogramCashbackAmount(authRecordedDTO.getAuthCryptogramCashbackAmount());
        authorizationLogVisa.setSecondaryPinBlock(authRecordedDTO.getAuthSecondaryPinBlock());
        authorizationLogVisa.setAuthTypeCode(authRecordedDTO.getAuthAuthTypeCode());
        authorizationLogVisa.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        authorizationLogVisa.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        authorizationLogVisa.setAuthCode(authRecordedDTO.getAuthAuthCode());
        authorizationLogVisa.setErrorCode(authRecordedDTO.getErrorDetailCode());
        authorizationLogVisa.setCardActivationResultCode(authRecordedDTO.getAuthCardActivationResultCode());
        authorizationLogVisa.setCardStatusResultCode(authRecordedDTO.getAuthCardStatusResultCode());
        authorizationLogVisa.setCardExpireDateResultCode(authRecordedDTO.getAuthCardExpireDateResultCode());
        authorizationLogVisa.setCardLimitResultCode(authRecordedDTO.getAuthCardLimitResultCode());
        authorizationLogVisa.setCardBlockResultCode(authRecordedDTO.getAuthCardBlockResultCode());
        authorizationLogVisa.setAccountStatusResultCode(authRecordedDTO.getAuthAccountStatusResultCode());
        authorizationLogVisa.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogVisa.setCustomerStatusResultCode(authRecordedDTO.getAuthCustomerStatusResultCode());
        authorizationLogVisa.setCustomerBlockResultCode(authRecordedDTO.getAuthCustomerBlockResultCode());
        authorizationLogVisa.setFraudFileResultCode(authRecordedDTO.getAuthFraudFileResultCode());
        authorizationLogVisa.setTerminalBlacklistResultCode(authRecordedDTO.getAuthTerminalBlacklistResultCode());
        authorizationLogVisa.setCvvResultCode(authRecordedDTO.getAuthCvvResultCode());
        authorizationLogVisa.setAvailLimitResultCode(authRecordedDTO.getAuthAvailLimitResultCode());
        authorizationLogVisa.setVelocityCheckResultCode(authRecordedDTO.getAuthVelocityCheckResultCode());
        authorizationLogVisa.setUsageCardResultCode(authRecordedDTO.getAuthUsageCardResultCode());
        authorizationLogVisa.setSingleLimitResultCode(authRecordedDTO.getAuthSingleLimitResultCode());
        authorizationLogVisa.setMaxApproveNmbrResultCode(authRecordedDTO.getAuthMaxApproveNmbrResultCode());
        authorizationLogVisa.setMaxDeclineNmbrResultCode(authRecordedDTO.getAuthMaxDeclineNmbrResultCode());
        authorizationLogVisa.setPinBlockResultCode(authRecordedDTO.getAuthPinBlockResultCode());
        authorizationLogVisa.setCvv2ResultCode(authRecordedDTO.getAuthCvv2ResultCode());
        authorizationLogVisa.setFisrtUsageResultCode(authRecordedDTO.getAuthFisrtUsageResultCode());
        //中间变量STATUS
        authorizationLogVisa.setTrancactionStatus(authRecordedDTO.getAuthTrancactionStatus());
        //中间变量TYPE
        authorizationLogVisa.setReversalType(authRecordedDTO.getAuthReversalType());
        authorizationLogVisa.setAuthTransactionSourceCode(authRecordedDTO.getAuthTransactionSourceCode());
        //《授权额度更新》处理后获取的limit_type_num
        authorizationLogVisa.setCreditLimitNodeId(authRecordedDTO.getAuthCreditLimitNodeId());
        authorizationLogVisa.setCustomerId(authRecordedDTO.getAuthCustomerId());
        authorizationLogVisa.setTransactionTypeCode(authRecordedDTO.getAuthTransactionTypeCode());
        authorizationLogVisa.setPartitionKey("");
        authorizationLogVisa.setCreateTime(LocalDateTime.now());
        authorizationLogVisa.setUpdateTime(LocalDateTime.now());
        authorizationLogVisa.setUpdateBy(Constant.DEFAULT_USER);
        authorizationLogVisa.setVersionNumber(1L);
        authorizationLogVisa.setMerchantFraudResultCode(authRecordedDTO.getMerchantFraudResultCode());
        authorizationLogVisa.setCardSafetyLockResultCode(authRecordedDTO.getAuthCardSecuritySwitchResultCode());
        authorizationLogVisa.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        authorizationLogVisa.setAnyCloudFraudResultCode(authRecordedDTO.getAuthAnyCloudFraudResultCode());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogVisa(BeanMapping.copy(authorizationLogVisa, AuthorizationLogVisaDTO.class), false);
            return 1;
        } else {
            return authorizationLogVisaMapper.insertSelective(authorizationLogVisa);
        }
    }

    /**
     * mc 授权流水数据更新
     */
    @BatchSharedAnnotation
    private int mcAddAuthLog(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogMc authorizationLogMc = new AuthorizationLogMc();
        authorizationLogMc.setAuthLogId(Optional.ofNullable(authRecordedDTO.getCurrentAuthLogId()).orElse(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        authorizationLogMc.setAuthLogTime(LocalDateTime.now());
        authorizationLogMc.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        authorizationLogMc.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        authorizationLogMc.setMessageTypeId(authRecordedDTO.getAuthMessageTypeId());
        authorizationLogMc.setCardNumber(authRecordedDTO.getAuthCardNumber());
        authorizationLogMc.setProcessingCode(authRecordedDTO.getAuthProcessingCode());
        authorizationLogMc.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        authorizationLogMc.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
       /* if(authRecordedDTO.getAuthReplaceAmountActualMC() != null){
            authorizationLogMC.setTransactionAmount(authRecordedDTO.getAuthReplaceAmountActualMC());
            authorizationLogMC.setCardholderBillingAmount(authRecordedDTO.getAuthReplaceAmountActualMC());
        }else{
            authorizationLogMC.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
            authorizationLogMC.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        }*/
        authorizationLogMc.setTransmissionTime(authRecordedDTO.getAuthTransmissionTime());
        authorizationLogMc.setCardholderBillingRate(authRecordedDTO.getAuthCardholderBillingRate().intValue());
        authorizationLogMc.setSystemTraceAuditNumber(authRecordedDTO.getAuthSystemTraceAuditNumber());
        authorizationLogMc.setLocalTransactionTime(authRecordedDTO.getAuthLocalTransactionTime());
        authorizationLogMc.setLocalTransactionDate(authRecordedDTO.getAuthLocalTransactionDate());
        authorizationLogMc.setCardExpirationDate(authRecordedDTO.getAuthCardExpirationDate());
        authorizationLogMc.setSettlementDate(authRecordedDTO.getAuthSettlementDate());
        authorizationLogMc.setMerchantType(authRecordedDTO.getAuthMerchantType());
        authorizationLogMc.setMerchantCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        authorizationLogMc.setServicePointEntryModeCode(authRecordedDTO.getAuthServicePointEntryModeCode());
        authorizationLogMc.setCardSequenceNumber(authRecordedDTO.getAuthCardSequenceNumber());
        authorizationLogMc.setServicePointConditionCode(authRecordedDTO.getAuthServicePointConditionCode());
        authorizationLogMc.setServicePointPinCode(authRecordedDTO.getAuthServicePointPinCaptureCode());
        authorizationLogMc.setTransactionFee(authRecordedDTO.getAuthTransactionFee());
        authorizationLogMc.setAcquiringIdentificationCode(authRecordedDTO.getAuthAcquiringIdentificationCode());
        authorizationLogMc.setForwardingIdentificationCode(authRecordedDTO.getAuthForwardingIdentificationCode());
        authorizationLogMc.setTrack2Data(authRecordedDTO.getAuthTrack2Data());
        authorizationLogMc.setRetrievalReferenceNumber(authRecordedDTO.getAuthRetrievalReferenceNumber());
        authorizationLogMc.setAuthIdentificationResponse(authRecordedDTO.getAuthAuthIdentificationResponse());
        authorizationLogMc.setResponseCode(authRecordedDTO.getAuthResponseCode());
        authorizationLogMc.setCardAcceptorTerminalCode(authRecordedDTO.getAuthCardAcceptorTerminalCode());
        authorizationLogMc.setCardAcceptorIdentification(authRecordedDTO.getAuthCardAcceptorIdentification());
        authorizationLogMc.setCardAcceptorNameLocation(authRecordedDTO.getAuthCardAcceptorNameLocation());
        authorizationLogMc.setAdditionalResponseData(authRecordedDTO.getAuthAdditionalResponseData());
        authorizationLogMc.setTrack1Data(authRecordedDTO.getAuthTrack1Data());
        authorizationLogMc.setAdditionalDataPrivate(authRecordedDTO.getAuthAdditionalDataPrivate());
        authorizationLogMc.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        authorizationLogMc.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
        authorizationLogMc.setPinData(authRecordedDTO.getAuthPinData());
        authorizationLogMc.setSecurityRelatedControlInfo(authRecordedDTO.getAuthSecurityRelatedControlInformation());
        authorizationLogMc.setAdditionalAmounts(authRecordedDTO.getAuthAdditionalAmounts());
        authorizationLogMc.setIccSystemRelatedData(authRecordedDTO.getAuthIccSystemRelatedData());
        authorizationLogMc.setNetworkManagementInfoCode(authRecordedDTO.getAuthNetworkManagementInformationCode());
        authorizationLogMc.setOriginalMessageTypeId(authRecordedDTO.getAuthOriginalMessageTypeId());
        authorizationLogMc.setOriginalSystemTraceNumber(authRecordedDTO.getAuthOriginalSystemTraceAuditNumber());
        authorizationLogMc.setOriginalTransmissionTime(authRecordedDTO.getAuthOriginalTransmissionTime());
        authorizationLogMc.setOriginalAcquiringIdeCode(authRecordedDTO.getAuthOriginalAcquiringIdentificationCode());
        authorizationLogMc.setOriginalForwardingIdeCode(authRecordedDTO.getAuthOriginalForwardingIdentificationCode());
        authorizationLogMc.setFileUpdateCode(authRecordedDTO.getAuthFileUpdateCode());
        authorizationLogMc.setRecInstitutionIdentifyCode(authRecordedDTO.getAuthReceivingInstitutionIdentificationCode());
        authorizationLogMc.setFileName(authRecordedDTO.getAuthFileName());
        authorizationLogMc.setAccountIdentification1(authRecordedDTO.getAuthAccountIdentification1());
        authorizationLogMc.setAccountIdentification2(authRecordedDTO.getAuthAccountIdentification2());
        authorizationLogMc.setNationalUse(authRecordedDTO.getAuthNationalUse());
        authorizationLogMc.setAuthTypeCode(authRecordedDTO.getAuthAuthTypeCode());
        authorizationLogMc.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        authorizationLogMc.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        authorizationLogMc.setAuthCode(authRecordedDTO.getAuthAuthCode());
        authorizationLogMc.setErrorCode(authRecordedDTO.getErrorDetailCode());
        authorizationLogMc.setCardActivationResultCode(authRecordedDTO.getAuthCardActivationResultCode());
        authorizationLogMc.setCardStatusResultCode(authRecordedDTO.getAuthCardStatusResultCode());
        authorizationLogMc.setCardExpireDateResultCode(authRecordedDTO.getAuthCardExpireDateResultCode());
        authorizationLogMc.setCardLimitResultCode(authRecordedDTO.getAuthCardLimitResultCode());
        authorizationLogMc.setCardBlockResultCode(authRecordedDTO.getAuthCardBlockResultCode());
        authorizationLogMc.setAccountStatusResultCode(authRecordedDTO.getAuthAccountStatusResultCode());
        authorizationLogMc.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogMc.setCustomerStatusResultCode(authRecordedDTO.getAuthCustomerStatusResultCode());
        authorizationLogMc.setCustomerBlockResultCode(authRecordedDTO.getAuthCustomerBlockResultCode());
        authorizationLogMc.setFraudFileResultCode(authRecordedDTO.getAuthFraudFileResultCode());
        authorizationLogMc.setTerminalBlacklistResultCode(authRecordedDTO.getAuthTerminalBlacklistResultCode());
        authorizationLogMc.setCvvResultCode(authRecordedDTO.getAuthCvvResultCode());
        authorizationLogMc.setAvailLimitResultCode(authRecordedDTO.getAuthAvailLimitResultCode());
        authorizationLogMc.setVelocityCheckResultCode(authRecordedDTO.getAuthVelocityCheckResultCode());
        authorizationLogMc.setUsageCardResultCode(authRecordedDTO.getAuthUsageCardResultCode());
        authorizationLogMc.setSingleLimitResultCode(authRecordedDTO.getAuthSingleLimitResultCode());
        authorizationLogMc.setMaxApproveNmbrResultCode(authRecordedDTO.getAuthMaxApproveNmbrResultCode());
        authorizationLogMc.setMaxDeclineNmbrResultCode(authRecordedDTO.getAuthMaxDeclineNmbrResultCode());
        authorizationLogMc.setPinBlockResultCode(authRecordedDTO.getAuthPinBlockResultCode());
        authorizationLogMc.setCvv2ResultCode(authRecordedDTO.getAuthCvv2ResultCode());
        authorizationLogMc.setFisrtUsageResultCode(authRecordedDTO.getAuthFisrtUsageResultCode());
        //中间变量STATUS
        authorizationLogMc.setTrancactionStatus(authRecordedDTO.getAuthTrancactionStatus());
        //中间变量TYPE
        authorizationLogMc.setReversalType(authRecordedDTO.getAuthReversalType());
        authorizationLogMc.setAuthTransactionSourceCode(authRecordedDTO.getAuthTransactionSourceCode());
        //《授权额度更新》处理后获取的limit_type_num
        authorizationLogMc.setCreditLimitNodeId(authRecordedDTO.getAuthCreditLimitNodeId());
        authorizationLogMc.setCustomerId(authRecordedDTO.getAuthCustomerId());
        authorizationLogMc.setTransactionTypeCode(authRecordedDTO.getAuthTransactionTypeCode());
        authorizationLogMc.setPartitionKey("");
        authorizationLogMc.setCreateTime(LocalDateTime.now());
        authorizationLogMc.setUpdateTime(LocalDateTime.now());
        authorizationLogMc.setUpdateBy(Constant.DEFAULT_USER);
        authorizationLogMc.setVersionNumber(1L);
        authorizationLogMc.setReplaceActualAmount(authRecordedDTO.getAuthReplaceAmountActualMc());
        authorizationLogMc.setReplaceSettleAmount(authRecordedDTO.getAuthReplaceAmountSettleMc());
        authorizationLogMc.setReplaceBillingAmount(authRecordedDTO.getAuthReplaceAmountBillingMc());
        authorizationLogMc.setMerchantFraudResultCode(authRecordedDTO.getMerchantFraudResultCode());
        authorizationLogMc.setCardSafetyLockResultCode(authRecordedDTO.getAuthCardSecuritySwitchResultCode());
        authorizationLogMc.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        authorizationLogMc.setAnyCloudFraudResultCode(authRecordedDTO.getAuthAnyCloudFraudResultCode());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogMc(BeanMapping.copy(authorizationLogMc, AuthorizationLogMcDTO.class), false);
            return 1;
        } else {
            return authorizationLogMcMapper.insertSelective(authorizationLogMc);
        }
    }

    /**
     * jcb 授权流水数据更新
     */
    @BatchSharedAnnotation
    private int jcbAddAuthLog(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogJcb authorizationLogJcb = new AuthorizationLogJcb();
        authorizationLogJcb.setAuthLogId(Optional.ofNullable(authRecordedDTO.getCurrentAuthLogId()).orElse(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        authorizationLogJcb.setAuthLogTime(LocalDateTime.now());
        authorizationLogJcb.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        authorizationLogJcb.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        authorizationLogJcb.setMessageTypeId(authRecordedDTO.getAuthMessageTypeId());
        authorizationLogJcb.setCardNumber(authRecordedDTO.getAuthCardNumber());
        authorizationLogJcb.setProcessingCode(authRecordedDTO.getAuthProcessingCode());
        if(authRecordedDTO.getAuthReplaceAmountActualJcb() != null){
            authorizationLogJcb.setTransactionAmount(authRecordedDTO.getAuthReplaceAmountActualJcb());
            authorizationLogJcb.setCardholderBillingAmount(authRecordedDTO.getAuthReplaceAmountActualJcb());
        }else{
            authorizationLogJcb.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
            authorizationLogJcb.setCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount());
        }
        authorizationLogJcb.setTransmissionTime(authRecordedDTO.getAuthTransmissionTime());
        authorizationLogJcb.setCardholderBillingRate(authRecordedDTO.getAuthCardholderBillingRate().intValue());
        authorizationLogJcb.setSystemTraceAuditNumber(authRecordedDTO.getAuthSystemTraceAuditNumber());
        authorizationLogJcb.setLocalTransactionTime(authRecordedDTO.getAuthLocalTransactionTime());
        authorizationLogJcb.setLocalTransactionDate(authRecordedDTO.getAuthLocalTransactionDate());
        authorizationLogJcb.setCardExpirationDate(authRecordedDTO.getAuthCardExpirationDate());
        authorizationLogJcb.setSettlementDate(authRecordedDTO.getAuthSettlementDate());
        authorizationLogJcb.setMerchantType(authRecordedDTO.getAuthMerchantType());
        authorizationLogJcb.setMerchantCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        authorizationLogJcb.setServicePointEntryModeCode(authRecordedDTO.getAuthServicePointEntryModeCode());
        authorizationLogJcb.setCardSequenceNumber(authRecordedDTO.getAuthCardSequenceNumber());
        authorizationLogJcb.setServicePointConditionCode(authRecordedDTO.getAuthServicePointConditionCode());
        authorizationLogJcb.setServicePointPinCode(authRecordedDTO.getAuthServicePointPinCaptureCode());
        authorizationLogJcb.setTransactionFee(authRecordedDTO.getAuthTransactionFee());
        authorizationLogJcb.setAcquiringIdentificationCode(authRecordedDTO.getAuthAcquiringIdentificationCode());
        authorizationLogJcb.setForwardingIdentificationCode(authRecordedDTO.getAuthForwardingIdentificationCode());
        authorizationLogJcb.setTrack2Data(authRecordedDTO.getAuthTrack2Data());
        authorizationLogJcb.setRetrievalReferenceNumber(authRecordedDTO.getAuthRetrievalReferenceNumber());
        authorizationLogJcb.setAuthIdentificationResponse(authRecordedDTO.getAuthAuthIdentificationResponse());
        authorizationLogJcb.setResponseCode(authRecordedDTO.getAuthResponseCode());
        authorizationLogJcb.setCardAcceptorTerminalCode(authRecordedDTO.getAuthCardAcceptorTerminalCode());
        authorizationLogJcb.setCardAcceptorIdentification(authRecordedDTO.getAuthCardAcceptorIdentification());
        authorizationLogJcb.setCardAcceptorNameLocation(authRecordedDTO.getAuthCardAcceptorNameLocation());
        authorizationLogJcb.setAdditionalResponseData(authRecordedDTO.getAuthAdditionalResponseData());
        authorizationLogJcb.setTrack3Data(authRecordedDTO.getAuthTrack3Data());
        authorizationLogJcb.setAdditionalDataPrivate(authRecordedDTO.getAuthAdditionalDataPrivate());
        authorizationLogJcb.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        authorizationLogJcb.setBillingCurrencyCode(authRecordedDTO.getAuthBillingCurrencyCode());
        authorizationLogJcb.setPinData(authRecordedDTO.getAuthPinData());
        authorizationLogJcb.setSecurityRelatedControlInfo(authRecordedDTO.getAuthSecurityRelatedControlInformation());
        authorizationLogJcb.setAdditionalAmounts(authRecordedDTO.getAuthAdditionalAmounts());
        authorizationLogJcb.setIccSystemRelatedData(authRecordedDTO.getAuthIccSystemRelatedData());
        authorizationLogJcb.setNetworkManagementInfoCode(authRecordedDTO.getAuthNetworkManagementInformationCode());
        authorizationLogJcb.setOriginalMessageTypeId(authRecordedDTO.getAuthOriginalMessageTypeId());
        authorizationLogJcb.setOriginalSystemTraceNumber(authRecordedDTO.getAuthOriginalSystemTraceAuditNumber());
        authorizationLogJcb.setOriginalTransmissionTime(authRecordedDTO.getAuthOriginalTransmissionTime());
        authorizationLogJcb.setOriginalAcquiringIdeCode(authRecordedDTO.getAuthOriginalAcquiringIdentificationCode());
        authorizationLogJcb.setOriginalForwardingIdeCode(authRecordedDTO.getAuthOriginalForwardingIdentificationCode());
        authorizationLogJcb.setRecInstitutionIdentifyCode(authRecordedDTO.getAuthReceivingInstitutionIdentificationCode());
        authorizationLogJcb.setFileName(authRecordedDTO.getAuthFileName());
        authorizationLogJcb.setAuthTypeCode(authRecordedDTO.getAuthAuthTypeCode());
        authorizationLogJcb.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        authorizationLogJcb.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        authorizationLogJcb.setAuthCode(authRecordedDTO.getAuthAuthCode());
        authorizationLogJcb.setErrorCode(authRecordedDTO.getErrorDetailCode());
        authorizationLogJcb.setCardActivationResultCode(authRecordedDTO.getAuthCardActivationResultCode());
        authorizationLogJcb.setCardStatusResultCode(authRecordedDTO.getAuthCardStatusResultCode());
        authorizationLogJcb.setCardExpireDateResultCode(authRecordedDTO.getAuthCardExpireDateResultCode());
        authorizationLogJcb.setCardLimitResultCode(authRecordedDTO.getAuthCardLimitResultCode());
        authorizationLogJcb.setCardBlockResultCode(authRecordedDTO.getAuthCardBlockResultCode());
        authorizationLogJcb.setAccountStatusResultCode(authRecordedDTO.getAuthAccountStatusResultCode());
        authorizationLogJcb.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogJcb.setCustomerStatusResultCode(authRecordedDTO.getAuthCustomerStatusResultCode());
        authorizationLogJcb.setCustomerBlockResultCode(authRecordedDTO.getAuthCustomerBlockResultCode());
        authorizationLogJcb.setFraudFileResultCode(authRecordedDTO.getAuthFraudFileResultCode());
        authorizationLogJcb.setTerminalBlacklistResultCode(authRecordedDTO.getAuthTerminalBlacklistResultCode());
        authorizationLogJcb.setCvvResultCode(authRecordedDTO.getAuthCvvResultCode());
        authorizationLogJcb.setAvailLimitResultCode(authRecordedDTO.getAuthAvailLimitResultCode());
        authorizationLogJcb.setVelocityCheckResultCode(authRecordedDTO.getAuthVelocityCheckResultCode());
        authorizationLogJcb.setUsageCardResultCode(authRecordedDTO.getAuthUsageCardResultCode());
        authorizationLogJcb.setSingleLimitResultCode(authRecordedDTO.getAuthSingleLimitResultCode());
        authorizationLogJcb.setMaxApproveNmbrResultCode(authRecordedDTO.getAuthMaxApproveNmbrResultCode());
        authorizationLogJcb.setMaxDeclineNmbrResultCode(authRecordedDTO.getAuthMaxDeclineNmbrResultCode());
        authorizationLogJcb.setPinBlockResultCode(authRecordedDTO.getAuthPinBlockResultCode());
        authorizationLogJcb.setCvv2ResultCode(authRecordedDTO.getAuthCvv2ResultCode());
        authorizationLogJcb.setFisrtUsageResultCode(authRecordedDTO.getAuthFisrtUsageResultCode());
        //中间变量STATUS
        authorizationLogJcb.setTrancactionStatus(authRecordedDTO.getAuthTrancactionStatus());
        //中间变量TYPE
        authorizationLogJcb.setReversalType(authRecordedDTO.getAuthReversalType());
        authorizationLogJcb.setAuthTransactionSourceCode(authRecordedDTO.getAuthTransactionSourceCode());
        //《授权额度更新》处理后获取的limit_type_num
        authorizationLogJcb.setCreditLimitNodeId(authRecordedDTO.getAuthCreditLimitNodeId());
        authorizationLogJcb.setCustomerId(authRecordedDTO.getAuthCustomerId());
        authorizationLogJcb.setTransactionTypeCode(authRecordedDTO.getAuthTransactionTypeCode());
        authorizationLogJcb.setPartitionKey("");
        authorizationLogJcb.setCreateTime(LocalDateTime.now());
        authorizationLogJcb.setUpdateTime(LocalDateTime.now());
        authorizationLogJcb.setUpdateBy(Constant.DEFAULT_USER);
        authorizationLogJcb.setVersionNumber(1L);
        authorizationLogJcb.setMerchantFraudResultCode(authRecordedDTO.getMerchantFraudResultCode());
        authorizationLogJcb.setCardSafetyLockResultCode(authRecordedDTO.getAuthCardSecuritySwitchResultCode());
        authorizationLogJcb.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        authorizationLogJcb.setAnyCloudFraudResultCode(authRecordedDTO.getAuthAnyCloudFraudResultCode());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogJcb(BeanMapping.copy(authorizationLogJcb, AuthorizationLogJcbDTO.class), false);
            return 1;
        } else {
            return authorizationLogJcbMapper.insertSelective(authorizationLogJcb);
        }
    }

    /**
     * jcb 授权流水数据更新
     */
    @BatchSharedAnnotation
    private int epccAddAuthLog(AuthRecordedDTO authRecordedDTO) {
        AuthorizationLogEpcc authorizationLogEpcc = new AuthorizationLogEpcc();
        authorizationLogEpcc.setAuthLogId(Optional.ofNullable(authRecordedDTO.getCurrentAuthLogId()).orElse(sequenceIdGen.generateId(TenantUtils.getTenantId())));
        authorizationLogEpcc.setAuthLogTime(LocalDateTime.now());
        authorizationLogEpcc.setGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        authorizationLogEpcc.setOriginalGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        authorizationLogEpcc.setCardNumber(authRecordedDTO.getAuthCardNumber());
        authorizationLogEpcc.setEpccTrxCode(authRecordedDTO.getEpccTrxCode());
        authorizationLogEpcc.setBizStsCd(null);
        authorizationLogEpcc.setTransactionAmount(authRecordedDTO.getAuthTransactionAmount());
        authorizationLogEpcc.setTransactionCurrency(authRecordedDTO.getAuthTransactionCurrencyCode());
        authorizationLogEpcc.setTransactionTypeTopCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        authorizationLogEpcc.setTransactionTypeDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        authorizationLogEpcc.setResponseCode(authRecordedDTO.getAuthResponseCode());
        authorizationLogEpcc.setCardActivationResultCode(authRecordedDTO.getAuthCardActivationResultCode());
        authorizationLogEpcc.setCardStatusResultCode(authRecordedDTO.getAuthCardStatusResultCode());
        authorizationLogEpcc.setCardExpireDateResultCode(authRecordedDTO.getAuthCardExpireDateResultCode());
        authorizationLogEpcc.setCardLimitResultCode(authRecordedDTO.getAuthCardLimitResultCode());
        authorizationLogEpcc.setCardBlockResultCode(authRecordedDTO.getAuthCardBlockResultCode());
        authorizationLogEpcc.setAccountStatusResultCode(authRecordedDTO.getAuthAccountStatusResultCode());
        authorizationLogEpcc.setAccountBlockResultCode(authRecordedDTO.getAuthAccountBlockResultCode());
        authorizationLogEpcc.setCustomerStatusResultCode(authRecordedDTO.getAuthCustomerStatusResultCode());
        authorizationLogEpcc.setCustomerBlockResultCode(authRecordedDTO.getAuthCustomerBlockResultCode());
        authorizationLogEpcc.setFraudFileResultCode(authRecordedDTO.getAuthFraudFileResultCode());
        authorizationLogEpcc.setTerminalBlacklistResultCode(authRecordedDTO.getAuthTerminalBlacklistResultCode());
        authorizationLogEpcc.setCvvResultCode(authRecordedDTO.getAuthCvvResultCode());
        authorizationLogEpcc.setAvailLimitResultCode(authRecordedDTO.getAuthAvailLimitResultCode());
        authorizationLogEpcc.setVelocityCheckResultCode(authRecordedDTO.getAuthVelocityCheckResultCode());
        authorizationLogEpcc.setUsageCardResultCode(authRecordedDTO.getAuthUsageCardResultCode());
        authorizationLogEpcc.setSingleLimitResultCode(authRecordedDTO.getAuthSingleLimitResultCode());
        authorizationLogEpcc.setMaxApproveNmbrResultCode(authRecordedDTO.getAuthMaxApproveNmbrResultCode());
        authorizationLogEpcc.setMaxDeclineNmbrResultCode(authRecordedDTO.getAuthMaxDeclineNmbrResultCode());
        authorizationLogEpcc.setPinBlockResultCode(authRecordedDTO.getAuthPinBlockResultCode());
        authorizationLogEpcc.setCvv2ResultCode(authRecordedDTO.getAuthCvv2ResultCode());
        authorizationLogEpcc.setFisrtUsageResultCode(authRecordedDTO.getAuthFisrtUsageResultCode());
        authorizationLogEpcc.setMerchantFraudResultCode(authRecordedDTO.getMerchantFraudResultCode());
        authorizationLogEpcc.setCardSafetyLockResultCode(authRecordedDTO.getAuthCardSecuritySwitchResultCode());
        authorizationLogEpcc.setIdNumberResultCode(null);
        authorizationLogEpcc.setIdTypeResultCode(null);
        authorizationLogEpcc.setNameResultCode(null);
        authorizationLogEpcc.setMobileNoResultCode(null);
        authorizationLogEpcc.setInternalSpecialResultCode(null);
        authorizationLogEpcc.setTerminalBlacklistResultCode(null);
        authorizationLogEpcc.setAuthCode(authRecordedDTO.getAuthAuthCode());
        authorizationLogEpcc.setErrorCode(authRecordedDTO.getErrorDetailCode());
        authorizationLogEpcc.setAuthTypeCode(authRecordedDTO.getAuthAuthTypeCode());
        authorizationLogEpcc.setAuthIdentificationResponse(authRecordedDTO.getAuthAuthIdentificationResponse());
        //中间变量STATUS
        authorizationLogEpcc.setTrancactionStatus(authRecordedDTO.getAuthTrancactionStatus());
        //中间变量TYPE
        authorizationLogEpcc.setReversalType(authRecordedDTO.getAuthReversalType());
        authorizationLogEpcc.setAuthTransactionSourceCode(authRecordedDTO.getAuthTransactionSourceCode());
        authorizationLogEpcc.setCustomerId(authRecordedDTO.getAuthCustomerId());
        authorizationLogEpcc.setTransactionTypeCode(authRecordedDTO.getAuthTransactionTypeCode());
        authorizationLogEpcc.setPostingTransactionCode(authRecordedDTO.getPostingTransactionCode());
        authorizationLogEpcc.setPostingTransactionCodeRev(authRecordedDTO.getPostingTransactionCodeRev());
        authorizationLogEpcc.setPartitionKey("");
        authorizationLogEpcc.setCreateTime(LocalDateTime.now());
        authorizationLogEpcc.setUpdateTime(LocalDateTime.now());
        authorizationLogEpcc.setUpdateBy(Constant.DEFAULT_USER);
        authorizationLogEpcc.setVersionNumber(1L);
        authorizationLogEpcc.setTrxId(authRecordedDTO.getTrxId());
        authorizationLogEpcc.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLogEpcc(BeanMapping.copy(authorizationLogEpcc, AuthorizationLogEpccDTO.class), false);
            return 1;
        } else {
            return authorizationLogEpccSelfMapper.insertSelective(authorizationLogEpcc);
        }
    }

    /**
     * 授权流水表更新
     * 如果是冲正、撤销、撤销冲正交易,需要更新原交易的授权流水记录
     * @param authRecordedDTO AuthRecordedDTO
     */
    @BatchSharedAnnotation
    private void authorizationLogUpdate(AuthRecordedDTO authRecordedDTO) {
        //本交易为撤销交易(authTransactionTypeCode为2)
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            getRevocationTrans(authRecordedDTO);
        }
        //本交易为冲正交易(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            getReversalTrans(authRecordedDTO);
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            //针对没有原交易的退货冲正做出的修改，不再去匹配需要原交易
            if(StringUtils.isBlank(authRecordedDTO.getAuthOriginalGlobalFlowNumber())) {
                return;
            }
            getRevocationReversal(authRecordedDTO);
        }
        // 本交易为退货交易(authTransactionTypeCode为6)
        if (AuthTransTypeEnum.REFUNDS_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            getRefundsTrans(authRecordedDTO);
        }
    }

    /**
     * 预授权流水表update
     * 预授权完成交易
     * @return int
     */
    @BatchSharedAnnotation
    private int preFinishAuthorizationLogUpdate(AuthRecordedDTO authRecordedDTO
            , PreAuthorizationLogDTO preAuthorizationLogDTO){
        //普通交易
        if (AuthTransTypeEnum.NORMAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return preFinishNormalTrans(preAuthorizationLogDTO,authRecordedDTO);
        }
        //本交易为撤销交易(authTransactionTypeCode为2)
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return preFinishRvocationTrans(preAuthorizationLogDTO,authRecordedDTO);
        }
        //本交易为冲正交易(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return preFinishReversalTrans(preAuthorizationLogDTO,authRecordedDTO);
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return preFinishRevocationReversal(preAuthorizationLogDTO,authRecordedDTO);
        }
        logger.error("The authTransactionTypeCode is not within the valid range of (1, 2, 3, 4). Data anomaly error!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 针对预授权完成操作
     * 授权流水表更新
     * 如果是冲正、撤销、撤销冲正交易,需要更新原交易的授权流水记录
     * @param authRecordedDTO AuthRecordedDTO
     */
    @BatchSharedAnnotation
    private int preFinishauthorizationLogUpdate(AuthRecordedDTO authRecordedDTO,PreAuthorizationLogDTO preAuthorizationLogDTO) {
        //本交易为普通交易(authTransactionTypeCode为1)
        if (AuthTransTypeEnum.NORMAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return getPreFinishNormalTrans(preAuthorizationLogDTO, authRecordedDTO);
        }
        //本交易为撤销交易(authTransactionTypeCode为2)
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return getPreFinishRevocationTrans(preAuthorizationLogDTO,authRecordedDTO);
        }
        //本交易为冲正交易(authTransactionTypeCode为3)
        if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return getPreFinishReversalTrans(preAuthorizationLogDTO,authRecordedDTO);
        }
        // 本交易为撤销冲正交易(authTransactionTypeCode为4)
        if (AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            return getPreFinishRevocationReversal(preAuthorizationLogDTO, authRecordedDTO);
        }
        logger.error("The authTransactionTypeCode is not within the valid range of (1, 2, 3, 4). Data anomaly error!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * REVOCATION_TRANS
     * @param authRecordedDTO
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getRevocationTrans(AuthRecordedDTO authRecordedDTO){
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (authorizationLogDTO != null) {
            authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
            authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
            authorizationLogDTO.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(authorizationLogDTO, true);
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            } else {
                int res = authorizationLogService.updateAuthorizationLogByPrimaryId(authorizationLogDTO);
                if(res == AuthConstans.ONE ){
                    return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
                }
            }
            logger.error("revocationTrans AuthorizationLog update error!");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        logger.error("original authorizationLog cannot be found!异常");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 预授权完成-普通
     * REVOCATION_TRANS
     *
     * @param preAuthorizationLogDTO PreAuthorizationLogDTO
     * @param authRecordedDTO
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getPreFinishNormalTrans(PreAuthorizationLogDTO preAuthorizationLogDTO, AuthRecordedDTO authRecordedDTO){
        //更新授权流水表
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(preAuthorizationLogDTO.getGlobalFlowNumber());
        if(authorizationLogDTO !=null){
            authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
            authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
            int res = updateAuthorizationLog(authorizationLogDTO);
            if(res == AuthConstans.ONE ){
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            }
            logger.error("preFinishNormalTrans failed: res={}", res);
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        logger.error("preFinishNormalTrans failed: authorizationLogDTO is null");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 预授权完成-撤销
     * REVOCATION_TRANS
     * @param preAuthorizationLogDTO PreAuthorizationLogDTO
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getPreFinishRevocationTrans(PreAuthorizationLogDTO preAuthorizationLogDTO,AuthRecordedDTO authRecordedDTO){
        //更新原预授权完成(授权流水表)
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if(authorizationLogDTO == null){
            logger.error("preFinishRevocationTrans failed: authorizationLogDTO is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
        authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
        updateAuthorizationLog(authorizationLogDTO);
        //更新原预授权请求(授权流水表)
        String globalFlowNumber = preAuthorizationLogDTO.getGlobalFlowNumber();
        AuthorizationLogDTO preOriginuthorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(globalFlowNumber);
        if(preOriginuthorizationLogDTO == null){
            logger.error("preFinishRevocationTrans failed: preOriginuthorizationLogDTO is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        preOriginuthorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        return  updateAuthorizationLog(preOriginuthorizationLogDTO);
    }

    /**
     * REVERSAL_TRANS
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getReversalTrans(AuthRecordedDTO authRecordedDTO){
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (authorizationLogDTO != null) {
            // 查询和验证类交易RefundedAmount初始为0
            if (authorizationLogDTO.getProcessingCode().startsWith("18") || authorizationLogDTO.getProcessingCode().startsWith("31")) {
                authRecordedDTO.setRefundedAmount(new BigDecimal(0));
            }
            BigDecimal refundedAmount = Objects.isNull(authRecordedDTO.getRefundedAmount()) ? BigDecimal.ZERO : authRecordedDTO.getRefundedAmount();
            if(authorizationLogDTO.getTransactionAmount().compareTo(authRecordedDTO.getAuthTransactionAmount()
                    .add(refundedAmount)) == 0){
                authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
                authorizationLogDTO.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
                authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
            }

            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(authorizationLogDTO, true);
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            }else {
                int res = authorizationLogService.updateAuthorizationLogByPrimaryId(authorizationLogDTO);
                if(res == AuthConstans.ONE ){
                    return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
                }
            }
            logger.error("AuthorizationLog update error!");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        logger.error(" original authorizationLog cannot be found!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 预授权完成-冲正
     * REVERSAL_TRANS
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @param preAuthorizationLogDTO {@link PreAuthorizationLogDTO}
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getPreFinishReversalTrans(PreAuthorizationLogDTO preAuthorizationLogDTO,AuthRecordedDTO authRecordedDTO){
        //更新原预授权完成记录(授权流水表)
//        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        List<AuthorizationLog> authorizationLogList = authorizationLogSelfMapper.upiSelectByCardNumberAndTransMission(authRecordedDTO.getAuthCardNumber(),authRecordedDTO.getAuthOriginalTransmissionTime());
        AuthorizationLog authorizationLog = authorizationLogList.stream().filter(a -> a.getSystemTraceAuditNumber().equals(authRecordedDTO.getAuthOriginalSystemTraceAuditNumber())).findFirst().orElse(null);
        if(authorizationLog == null){
            logger.error("preFinishReversalTrans failed: authorizationLog is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        AuthorizationLogDTO authorizationLogDTO = BeanMapping.copy(authorizationLog, AuthorizationLogDTO.class);
        authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
        authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
        updateAuthorizationLog(authorizationLogDTO);

        //更新原预授权请求(授权流水表)
        String globalFlowNumber = preAuthorizationLogDTO.getGlobalFlowNumber();
        AuthorizationLogDTO preOriginuthorizationLogDTO = authorizationLogService.getByGlobalFlowNumber(globalFlowNumber);
        if(preOriginuthorizationLogDTO == null){
            logger.error("preFinishReversalTrans failed: preOriginuthorizationLogDTO is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        preOriginuthorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        return updateAuthorizationLog(preOriginuthorizationLogDTO);
    }

    /**
     * REVOCATION_REVERSAL_STATUS
     * @param authRecordedDTO
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getRevocationReversal(AuthRecordedDTO authRecordedDTO){
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        if(authorizationLogDTO == null){
            logger.error("authorizationLogDTO cannot be found！");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
        authorizationLogDTO.setReversalType(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
        authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(authorizationLogDTO, true);
        }else {
            int res = authorizationLogService.updateAuthorizationLogByPrimaryId(authorizationLogDTO);
            if(res != AuthConstans.ONE){
                logger.error("Authorization log update error!{}",res);
                return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
            }
        }

        AuthorizationLogDTO orignalAuthorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if(orignalAuthorizationLogDTO == null){
            logger.error("orignalAuthorizationLogDTO cannot be found！");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        orignalAuthorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        orignalAuthorizationLogDTO.setReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(authorizationLogDTO, true);
            return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }else {
            int res = authorizationLogService.updateAuthorizationLogByPrimaryId(orignalAuthorizationLogDTO);
            if(res == AuthConstans.ONE ){
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            }
        }
        logger.error("AuthorizationLog update error!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }
    /**
     * REFUND_STATUS
     * @param authRecordedDTO
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getRefundsTrans(AuthRecordedDTO authRecordedDTO){
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if (authorizationLogDTO != null) {
            BigDecimal refundedAmount = Objects.isNull(authRecordedDTO.getRefundedAmount()) ? BigDecimal.ZERO : authRecordedDTO.getRefundedAmount();
            authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(authorizationLogDTO.getGlobalFlowNumber(), authorizationLogDTO.getLocalTransactionTime(), authorizationLogDTO.getLocalTransactionDate(), authorizationLogDTO.getSystemTraceAuditNumber()));
            if(authorizationLogDTO.getTransactionAmount().compareTo(authRecordedDTO.getAuthTransactionAmount().add(refundedAmount))>0){
                authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.PART_REFUND_STATUS.getCode());
                authorizationLogDTO.setReversalType(ReversalTypeEnum.REVOCATION_PART_REFUNDS_TRANS.getCode());
            }else{
                authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.WHOLE_REFUND_STATUS.getCode());
                authorizationLogDTO.setReversalType(ReversalTypeEnum.REVOCATION_WHOLE_REFUNDS_TRANS.getCode());
            }
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(authorizationLogDTO, true);
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            } else {
                int res = authorizationLogService.updateAuthorizationLogByPrimaryId(authorizationLogDTO);
                if(res == AuthConstans.ONE ){
                    return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
                }
            }
            logger.error("refundsTrans AuthorizationLog update error!");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        logger.error("original authorizationLog cannot be found!");
        return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
    }

    /**
     * 预授权完成 - 撤销冲正
     * REVOCATION_REVERSAL_STATUS
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @param preAuthorizationLogDTO {@link PreAuthorizationLogDTO}
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int getPreFinishRevocationReversal(PreAuthorizationLogDTO preAuthorizationLogDTO
            ,AuthRecordedDTO authRecordedDTO){
        //更新原预授权完成撤销记录(授权流水表)
        AuthorizationLogDTO revoAuthorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(authRecordedDTO.getAuthOriginalGlobalFlowNumber());
        if(revoAuthorizationLogDTO == null){
            logger.error("preFinishRevocationReversal failed: revoAuthorizationLogDTO is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        authRecordedDTO.setPartnerOriginTransInfoDTO(new PartnerOriginTransInfoDTO(revoAuthorizationLogDTO.getGlobalFlowNumber(), revoAuthorizationLogDTO.getLocalTransactionTime(), revoAuthorizationLogDTO.getLocalTransactionDate(), revoAuthorizationLogDTO.getSystemTraceAuditNumber()));
        revoAuthorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVERSAL_STATUS.getCode());
        updateAuthorizationLog(revoAuthorizationLogDTO);

        //更新原预授权完成记录(授权流水表)
        AuthorizationLogDTO authorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(preAuthorizationLogDTO.getOriginalGlobalFlowNumber());
        if(authorizationLogDTO == null){
            logger.error("preFinishRevocationReversal failed: authorizationLogDTO is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        authorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        updateAuthorizationLog(authorizationLogDTO);

        //更新原预授权请求(授权流水表)
        String globalFlowNumber = authorizationLogDTO.getOriginalGlobalFlowNumber();
        AuthorizationLogDTO preOriginuthorizationLogDTO = getAuthorizationLogByGlobalFlowNumber(globalFlowNumber);
        if(preOriginuthorizationLogDTO == null){
            logger.error("preFinishRevocationReversal failed: preOriginuthorizationLogDTO is null");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        preOriginuthorizationLogDTO.setTrancactionStatus(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
        return updateAuthorizationLog(preOriginuthorizationLogDTO);
    }

    @BatchSharedAnnotation
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int modifyCardAuthorizationInAuthPhase(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload){
        //获取检查项
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        //update
        cardAuthorizationDTO.setUpdateTime(LocalDateTime.now());
        cardAuthorizationDTO.setUpdateBy(Constant.DEFAULT_USER);
        int res = 0;
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
            res = AuthConstans.ONE;
        } else {
            res = this.cardAuthorizationInfoMapper.updateByPrimaryKeyInAuthPhase(cardAuthorizationDTO);
        }
        if(res != AuthConstans.ONE){
           return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }else {
            cardAuthorizationDTO.setVersionNumber(cardAuthorizationDTO.getVersionNumber() + 1);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 卡片授权信息更新处理
     * @param authorizationCheckProcessingPayload
     * @ int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    public int modifyCardAuthorization(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        //获取检查项
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        cardAuthorizationDTO.setVersionNumber(cardAuthorizationDTO.getVersionNumber() + 1);
        //update
        cardAuthorizationDTO.setUpdateTime(LocalDateTime.now());
        cardAuthorizationDTO.setUpdateBy(Constant.DEFAULT_USER);
        int res = 1;
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
        } else {
            res = this.cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class));
        }

        if(res != AuthConstans.ONE){
            logger.error("cardAuthorization update error!");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 构建卡片授权限额相关字段
     * @param authorizationCheckProcessingPayload authorizationCheckProcessingDTO
     */
    public void buildCardAutByAuthLlimit(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        AccountManagementInfoDTO accountManageInfo = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

        List<ParmAuthCheckControlDTO> authCheckControlDTOList = authorizationCheckProcessingPayload.getParmAuthCheckControlDTOList();
        //卡片授权信息表set
        cardAuthorizationSet(cardAuthorizationDTO,authCheckControlDTOList);
        BigDecimal txnAmt = authRecordedDTO.getAuthCardholderBillingAmount();

        //卡片授权信息表set2
        cardAuthorizationLimitSet(authRecordedDTO,accountManageInfo,cardAuthorizationDTO,txnAmt);
    }


    /**
     * 额度更新
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     */
    @BatchSharedAnnotation
    public void limitUpdate(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        SystemTableDTO systemInfo = authorizationCheckProcessingPayload.getSystemInfo();
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        boolean updateFlag = ! authRecordedDTO.getPreAuthComplete()
                && (StringUtils.isNotBlank(authRecordedDTO.getPostingTransactionCodeRev())
                || !AuthConstans.I.equals(systemInfo.getLimitUpdateFlag()));
        if(updateFlag){
            logger.warn("Real-time posting transaction code has value or system limit update flag is not enabled, no limit update will be performed!");
            return ;
        }
        logger.info("Calling limitRequestPrepareService.updateLimit");
        limitRequestPrepareService.updateLimit(authorizationCheckProcessingPayload);
        logger.info("limitRequestPrepareService.updateLimit completed");
    }

    /**
     * 预授权完成额度更新
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     */
    @BatchSharedAnnotation
    public int limitUpdateForPreAuth(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,PreAuthorizationLogDTO preAuthorizationLogDTO) {
        SystemTableDTO systemInfo = authorizationCheckProcessingPayload.getSystemInfo();
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        CardAuthorizationDTO cardAuthorizationInfo = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        AccountManagementInfoDTO accountManagementInfoDTO = authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
        CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = authorizationCheckProcessingPayload.getCustomerAuthorizationInfoDTO();

        if(!AuthConstans.I.equals(systemInfo.getLimitUpdateFlag())){
            logger.info("System limit update flag is not enabled, no limit update will be performed!");
            return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }

        if(authorizationCheckProcessingPayload.getLimitCheckResult( ) == null){
            //当预授权完成未经过额度检查时, 调用交易路由规则,获取预授权完成的交易金额检查
            logger.info("Calling authMatchRuleService.limitCtrlUnitOccupiedResult for pre-authorization completion");
            authMatchRuleService.limitCtrlUnitOccupiedResult(orgInfo,
                    cardAuthorizationInfo, Optional.ofNullable(accountManagementInfoDTO).map(AccountManagementInfoDTO::getAccountManagementId).orElse(""), authRecordedDTO, customerAuthorizationInfoDTO);
            logger.info("authMatchRuleService.limitCtrlUnitOccupiedResult completed for pre-authorization completion");

            if (CollectionUtils.isEmpty(authRecordedDTO.getLimitUnitList())){
                //可用额度不够
                logger.error("Available limit is insufficient for pre-authorization completion");
                return AuthItemCheckResCodeEnum.ERROR_CODE.getCode();
            }
        }
        
        logger.info("Calling limitRequestPrepareService.updateLimitForPreAuthComplete");
        limitRequestPrepareService.updateLimitForPreAuthComplete(authorizationCheckProcessingPayload,
                preAuthorizationLogDTO);
        logger.info("limitRequestPrepareService.updateLimitForPreAuthComplete completed");

        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * cardAuthorization update
     */
    public static void cardAuthorizationSet(CardAuthorizationDTO cardAuthorizationDTO
            , List<ParmAuthCheckControlDTO> authCheckControlDTOList){
        int pinTriesCount = cardAuthorizationDTO.getPinTriesCount() == null ? 0:cardAuthorizationDTO.getPinTriesCount();
        int cvvTriesCount = cardAuthorizationDTO.getCvvTriesCount() == null ? 0:cardAuthorizationDTO.getCvvTriesCount();
        int cvv2TriesCount = cardAuthorizationDTO.getCvv2TriesCount() == null ? 0:cardAuthorizationDTO.getCvv2TriesCount();
        int dlyAcctErrCnt = cardAuthorizationDTO.getDlyAcctErrCnt() == null ? 0:cardAuthorizationDTO.getDlyAcctErrCnt();
        if(!CollectionUtils.isEmpty(authCheckControlDTOList)){
            for (ParmAuthCheckControlDTO parmAuthCheckControlDto : authCheckControlDTOList) {
                if (StringUtils.isNotEmpty(parmAuthCheckControlDto.getCheckItem())) {
                    AuthCheckItemEnum authCheckItemEnum = AuthCheckItemEnum.getEnum(parmAuthCheckControlDto.getCheckItem());
                    if (null != authCheckItemEnum) {
                        switch (authCheckItemEnum) {
                            case CVV:
                                if (AuthConstans.AUTH_CHECK_RESULT_APPROVE.equals(parmAuthCheckControlDto.getCheckResult()) && cvvTriesCount != AuthConstans.ZERO) {
                                    cardAuthorizationDTO.setCvvTriesCount(AuthConstans.ZERO);
                                }
                                break;
                            case CVV2:
                                if (AuthConstans.AUTH_CHECK_RESULT_APPROVE.equals(parmAuthCheckControlDto.getCheckResult()) && cvv2TriesCount != AuthConstans.ZERO) {
                                    cardAuthorizationDTO.setCvv2TriesCount(AuthConstans.ZERO);
                                }
                                break;
                            case PASS_WORD:
                                if (AuthConstans.AUTH_CHECK_RESULT_APPROVE.equals(parmAuthCheckControlDto.getCheckResult()) && pinTriesCount != AuthConstans.ZERO) {
                                    cardAuthorizationDTO.setPinTriesCount(AuthConstans.ZERO);
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
    }

    /**
     * card limit set
     */
    private CardAuthorizationDTO cardAuthorizationLimitSet(AuthRecordedDTO authRecordedDTO, AccountManagementInfoDTO accountManagementInfoDTO,
                                                           CardAuthorizationDTO cardAuthorizationDTO,BigDecimal txnAmt) {
        //管理账户上一账单日次日获取
        LocalDate lastState = accountManagementInfoDTO.getLastStatementDate();
        LocalDate cleCtrSttDte = cardAuthorizationDTO.getCleCtrSttDte();
        if (lastState != null) {
            lastState = lastState.plusDays(1);
        } else {
            lastState = accountManagementInfoDTO.getOpenDate();
        }
        if(lastState.equals(cleCtrSttDte)){
            if(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode())){
                cardAuthorizationDTO.setBillingCycleRetailAmount(cardAuthorizationDTO.getBillingCycleCashAmount()
                        .add(txnAmt));
            }
            if(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode())){
                cardAuthorizationDTO.setBillingCycleCashAmount(cardAuthorizationDTO.getBillingCycleCashLimit()
                        .add(txnAmt));
            }
            return cardAuthorizationDTO;
        }
        if(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode())){
            cardAuthorizationDTO.setBillingCycleRetailAmount(txnAmt);
            cardAuthorizationDTO.setBillingCycleCashAmount(BigDecimal.ZERO);
        }
        if(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode())){
            cardAuthorizationDTO.setBillingCycleRetailAmount(BigDecimal.ZERO);
            cardAuthorizationDTO.setBillingCycleCashAmount(txnAmt);
            cardAuthorizationDTO.setCleCtrSttDte(lastState);
        }
        return cardAuthorizationDTO;
    }

    /**
     * 普通消费
     * @param cardAuthorizationInfo {@link CardAuthorizationDTO}
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @return  approve:0,exception:-2
     */
    public int modifyCardAuthorizationRetail(CardAuthorizationDTO cardAuthorizationInfo, AuthRecordedDTO authRecordedDTO) {
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                || AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            cardAuthorizationInfo.setBillingCycleRetailAmount(cardAuthorizationInfo.getBillingCycleRetailAmount().
                    subtract(authRecordedDTO.getAuthTransactionAmount()));
        }
        cardAuthorizationInfo.setBillingCycleRetailAmount(cardAuthorizationInfo.getBillingCycleRetailAmount().
                add(authRecordedDTO.getAuthTransactionAmount()));
        cardAuthorizationInfo.setUpdateTime(LocalDateTime.now());
        cardAuthorizationInfo.setUpdateBy(Constant.DEFAULT_USER);
        cardAuthorizationInfo.setVersionNumber(1L);

        int res = cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationInfo.class));
        if(res != AuthConstans.ONE){
            logger.error("cardAuthorizationInfo update error! data anomaly,{}",res);
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 取现
     * @return int  approve:0,exception:-2
     */
    public int cardAuthorizationCashUpdate(CardAuthorizationDTO cardAuthorizationInfo, AuthRecordedDTO authRecordedDTO) {
        if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())
                || AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
            cardAuthorizationInfo.setBillingCycleRetailAmount(cardAuthorizationInfo.getBillingCycleRetailAmount().
                    subtract(authRecordedDTO.getAuthTransactionAmount()));
        }
        cardAuthorizationInfo.setBillingCycleCashAmount(cardAuthorizationInfo.getBillingCycleRetailAmount().
                add(authRecordedDTO.getAuthTransactionAmount()));
        cardAuthorizationInfo.setUpdateTime(LocalDateTime.now());
        cardAuthorizationInfo.setUpdateBy(Constant.DEFAULT_USER);
        cardAuthorizationInfo.setVersionNumber(1L);
        int res = cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationInfo.class));
        if(res != AuthConstans.ONE){
            logger.error("cardAuthorizationInfo update error! data anomaly,{}",res);
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }



    @BatchSharedAnnotation
    public AuthorizationLogExpress getExpressAuthorizationLogByGlobalFlowNumber(String globalFlowNumber){
        AuthorizationLogExpress result = null;
        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            AuthorizationLogExpressDTO cache = CustAccountBO.threadCustAccountBO.get().getAuthBO().getAuthorizationLogExpressByGlobalFlowNumber(globalFlowNumber);
            if (cache != null) {
                result = BeanMapping.copy(cache, AuthorizationLogExpress.class);
            }
        }

        if (result == null) {
            result = authorizationLogExpressMapper.selectByGlobalFlowNumber(globalFlowNumber);
        }

        return result;
    }


    /**
     * 基于流水号获取预授权流水
     * @param globalFlowNumber 流水号
     * @return {@link PreAuthorizationLogDTO}
     */
    @BatchSharedAnnotation
    public PreAuthorizationLogDTO getPreAuthorizationLogByGlobalFlowNumber(String globalFlowNumber){
        PreAuthorizationLogDTO result = null;
        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            result = CustAccountBO.threadCustAccountBO.get().getAuthBO().getPreAuthorizationLogByGlobalFlowNum(globalFlowNumber);
        }
        if (result == null) {
            PreAuthorizationLog preAuthorizationLog = preAuthorizationLogSelfMapper.selectByGlobalFlowNumber(globalFlowNumber);
            if(preAuthorizationLog != null){
                result = BeanMapping.copy(preAuthorizationLog, PreAuthorizationLogDTO.class);
            }
        }
        return result;
    }

    /**
     * 基于流水号获取visa授权流水
     * @param globalFlowNumber 流水号
     * @return {@link AuthorizationLogVisa}
     */
    @BatchSharedAnnotation
    public AuthorizationLogVisa getAuthorizationLogVisaByGlobalFlowNumber(String globalFlowNumber){
        AuthorizationLogVisa result = null;

        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            AuthorizationLogVisaDTO cache = CustAccountBO.threadCustAccountBO.get().getAuthBO().getAuthorizationLogVisaByGlobalFlowNum(globalFlowNumber);
            if (cache != null) {
                result = BeanMapping.copy(cache, AuthorizationLogVisa.class);
            }
        }

        if (result == null) {
            result = authorizationLogVisaSelfMapper.selectByGlobalFlowNumber(globalFlowNumber);
        }

        return result;
    }

    /**
     * 基于流水号获取visa授权流水
     * @param globalFlowNumber 流水号
     * @return {@link AuthorizationLogVisa}
     */
    @BatchSharedAnnotation
    public AuthorizationLogEpcc getAuthorizationLogEpccByGlobalFlowNumber(String globalFlowNumber){
        AuthorizationLogEpcc result = null;

        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            AuthorizationLogEpccDTO cache = CustAccountBO.threadCustAccountBO.get().getAuthBO().getAuthorizationLogEpccByGlobalFlowNum(globalFlowNumber);
            if (cache != null) {
                result = BeanMapping.copy(cache, AuthorizationLogEpcc.class);
            }
        }

        if (result == null) {
            result = authorizationLogEpccSelfMapper.selectByGlobalFlowNumber(globalFlowNumber);
        }

        return result;
    }

    /**
     * 基于流水号获取JCB授权流水
     * @param globalFlowNumber ；流水号
     * @return {@link AuthorizationLogJcb}
     */
    @BatchSharedAnnotation
    public AuthorizationLogJcb getAuthorizationLogJcbByGlobalFlowNumber(String globalFlowNumber){
        AuthorizationLogJcb result = null;

        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            AuthorizationLogJcbDTO cache = CustAccountBO.threadCustAccountBO.get().getAuthBO().getAuthorizationLogJcbByGlobalFlowNum(globalFlowNumber);
            if (cache != null) {
                result = BeanMapping.copy(cache, AuthorizationLogJcb.class);
            }
        }

        if (result == null) {
            result = authorizationLogJcbSelfMapper.selectByGlobalFlowNumber(globalFlowNumber);
        }

        return result;
    }

    /**
     * 基于流水号获取MC授权流水
     * @param globalFlowNumber 流水号
     * @return {@link AuthorizationLogMc}
     */
    @BatchSharedAnnotation
    public AuthorizationLogMc getAuthorizationLogMcByGlobalFlowNumber(String globalFlowNumber){
        AuthorizationLogMc result = null;

        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            AuthorizationLogMcDTO cache = CustAccountBO.threadCustAccountBO.get().getAuthBO().getAuthorizationLogMcByGlobalFlowNum(globalFlowNumber);
            if (cache != null) {
                result = BeanMapping.copy(cache, AuthorizationLogMc.class);
            }
        }

        if (result == null) {
            result = authorizationLogMcSelfMapper.selectByGlobalFlowNumber(globalFlowNumber);
        }

        return result;
    }

    /**
     * 基于流水号获取授权流水
     * @param globalFlowNumber 流水号
     * @return {@link AuthorizationLogDTO}
     */
    @BatchSharedAnnotation
    public AuthorizationLogDTO getAuthorizationLogByGlobalFlowNumber(String globalFlowNumber){
        AuthorizationLogDTO result = null;

        // 批次先尝试从缓存获取
        if (CustAccountBO.isBatch()) {
            result = CustAccountBO.threadCustAccountBO.get().getAuthBO().getAuthorizationLogByGlobalFlowNum(globalFlowNumber);
        }

        if (result == null) {
            result = authorizationLogService.getByGlobalFlowNumber(globalFlowNumber);
        }

        return result;
    }

    /**
     * 更新授权流水
     * @return 影响行数
     */
    public int updateAuthorizationLog(AuthorizationLogDTO authorizationLogDTO){
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateAuthorizationLog(authorizationLogDTO, true);
            return 1;
        } else {
            return authorizationLogService.updateAuthorizationLogByPrimaryId(authorizationLogDTO);
        }
    }


    /**
     * 账户验证交易（在交易识别时，MTI=0100  Processing Code以33开头，形如33X000）
     * 更新证错误次数累积日期和次数
     * @param cardAuthorizationInfo
     * @param authRecordedDTO
     * @param orgInfo
     * @param isAuthPass
     */
    public static void updateDlyAcctErrCnt(CardAuthorizationDTO cardAuthorizationInfo, AuthRecordedDTO authRecordedDTO,OrganizationInfoResDTO orgInfo,Boolean isAuthPass){
        //账户验证交易（在交易识别时，MTI=0100  Processing Code以33开头，形如33X000）
        if (Objects.equals("0100",authRecordedDTO.getAuthMessageTypeId())
                && authRecordedDTO.getAuthProcessingCode().startsWith("33")){
            logger.info("Account verification transaction, updating cumulative verification error count");
            if(isAuthPass){
                if(cardAuthorizationInfo.getDlyAcctErrCnt() >0 ){
                    cardAuthorizationInfo.setDlyAcctErrCnt(AuthConstans.ZERO);
                    cardAuthorizationInfo.setDlyAcctSttDte(orgInfo.getNextProcessingDay());
                }
            }else {
                if(null == cardAuthorizationInfo.getDlyAcctSttDte() || orgInfo.getNextProcessingDay().compareTo(cardAuthorizationInfo.getDlyAcctSttDte()) == 0){
                    cardAuthorizationInfo.setDlyAcctErrCnt(cardAuthorizationInfo.getDlyAcctErrCnt() + 1);
                }else {
                    cardAuthorizationInfo.setDlyAcctErrCnt(1);
                    cardAuthorizationInfo.setDlyAcctSttDte(orgInfo.getNextProcessingDay());
                }
            }
            logger.info("DlyAcctErrCnt:{},DlyAcctSttDte:{}",cardAuthorizationInfo.getDlyAcctSttDte(),cardAuthorizationInfo.getDlyAcctSttDte());
        }
    }
}
