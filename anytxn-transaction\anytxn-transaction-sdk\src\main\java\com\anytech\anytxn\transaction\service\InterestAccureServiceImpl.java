package com.anytech.anytxn.transaction.service;

import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.account.mapper.PmInterestHisSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlService;
import com.anytech.anytxn.parameter.base.account.service.IInterestBearingService;
import com.anytech.anytxn.parameter.base.account.service.IInterestSettlementService;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.rule.limit.mapper.broadcast.LimitUnitDefiMapper;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerBasicInfoSelfMapper;
import com.anytech.anytxn.business.base.transaction.domain.dto.InterestLogHistoryDTO;
import com.anytech.anytxn.limit.service.LimitMatchCheckService;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestBearingDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.InterestSettlementDTO;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.domain.bo.InterestBO;
import com.anytech.anytxn.transaction.base.domain.bo.InterestCalcResultBO;
import com.anytech.anytxn.transaction.base.domain.bo.InterestLogBO;
import com.anytech.anytxn.transaction.base.domain.bo.InterestParamsBO;
import com.anytech.anytxn.transaction.base.domain.bo.InterestProcessResultBO;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.enums.BatchInterestEnum;
import com.anytech.anytxn.transaction.base.enums.CreateMethodEnum;
import com.anytech.anytxn.transaction.base.enums.GraceOptionEnum;
import com.anytech.anytxn.transaction.base.enums.InterestAccrualMethodEnum;
import com.anytech.anytxn.transaction.base.enums.InterestCalculateOptionEnum;
import com.anytech.anytxn.transaction.base.enums.InterestProssingIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.MessageIndicatorEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.service.IInterestAccureCalcService;
import com.anytech.anytxn.transaction.base.service.IInterestAccureInnerService;
import com.anytech.anytxn.transaction.base.service.IInterestAccureService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedCacheAccountBalanceService;
import com.anytech.anytxn.transaction.base.utils.InterestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 〈利息累积对外接口服务〉
 *
 * <AUTHOR>
 * @create 2018/9/15 17:04
 */
@Service
public class InterestAccureServiceImpl implements IInterestAccureService {

    private static final Logger logger = LoggerFactory.getLogger(InterestAccureServiceImpl.class);

    @Autowired
    private IInterestAccureInnerService interestAccureInnerService;


    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;

    @Autowired
    private IDelinquentControlService delinquentControlService;

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private ITxnRecordedService txnRecordedService;

    @Autowired
    private IInterestAccureCalcService interestAccureCalcService;

    @Autowired
    private ITransactionCodeService transactionCodeService;

    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IInterestBearingService interestBearingService;
    @Autowired
    private IInterestSettlementService interestSettlementService;

    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private ITransactionTypeService transactionTypeService;

    @Autowired
    private LimitMatchCheckService limitMatchCheckService;
    @Autowired
    private LimitUnitDefiMapper limitUnitDefiMapper;
    @Autowired
    private TxnRecordedCacheAccountBalanceService txnRecordedCacheAccountBalanceService;
    @Autowired
    private IStatementProcessService statementProcessService;
    @Autowired
    private PmInterestHisSelfMapper pmInterestHisSelfMapper;
    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Autowired
    private ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public InterestProcessResultBO accureInterest(AccountBalanceInfoDTO accountBalanceInfo,
                                                  AccountManagementInfoDTO accountManagementInfoDTO,
                                                  RecordedBO recorded) {
        InterestProcessResultBO interestProcessResultBO = null;
        OrganizationInfoResDTO paramOrganizationInfo = getParamOrganizationInfo(
                accountBalanceInfo);
        if (accountBalanceInfo.getCurrentEffectInterestRate() != null &&
                InterestAccrualMethodEnum.EVENT_ACCRUAL.getCode().equals(paramOrganizationInfo.getInterestAccrualMethod())) {
            Boolean calculateAccrueInterestFlag = beforeCalculateAccrueInterest(accountBalanceInfo, accountManagementInfoDTO);
            if (!calculateAccrueInterestFlag) {
                return null;
            }
            logger.info("Calling interestAccureInnerService.accureInterest, accountId={}", accountBalanceInfo.getTransactionBalanceId());
            interestProcessResultBO = interestAccureInnerService
                    .accureInterest(accountBalanceInfo, accountManagementInfoDTO, recorded);
            logger.info("interestAccureInnerService.accureInterest call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
        }
        return interestProcessResultBO;
    }


    @Override
    public InterestProcessResultBO accureAndBackInterestForRepayment(AccountBalanceInfoDTO accountBalanceInfo,
                                                                     AccountManagementInfoDTO accountManagementInfoDTO, RecordedMidBO recordedMidBo, BigDecimal balance) {
        InterestProcessResultBO interestProcessResultBO = null;
        if (StringUtils.isNotBlank(accountBalanceInfo.getInterestAccrualTableId())) {
            InterestBearingDTO paramAccureInterest = getParamAccureInterest(accountBalanceInfo);
            OrganizationInfoResDTO paramOrganizationInfo = getParamOrganizationInfo(accountBalanceInfo);
            if (InterestAccrualMethodEnum.EVENT_ACCRUAL.getCode().equals(paramOrganizationInfo.getInterestAccrualMethod())) {
                Boolean calculateAccrueInterestFlag = beforeCalculateAccrueInterest(accountBalanceInfo, accountManagementInfoDTO);
                if (!calculateAccrueInterestFlag) {
                    return null;
                }
                logger.info("Calling interestAccureInnerService.accureInterest, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                interestProcessResultBO = interestAccureInnerService.accureInterest(accountBalanceInfo
                        , accountManagementInfoDTO, recordedMidBo.recorded);
                logger.info("interestAccureInnerService.accureInterest call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
            } else {
                interestProcessResultBO = new InterestProcessResultBO(accountBalanceInfo.getInterestAccrueDate(),
                        accountBalanceInfo.getLastInterestAccrueDate(), accountBalanceInfo.getAccrueInterest());
            }
            //利率分段回算用的一段逻辑。源自CTBC分支。
            // 借贷分离后利率在交易账户上，分不了段，注释掉
            //有免息期
//            if(GraceOption.HAVE.getCode().equals(paramSettleInterest.getGraceOption())){
//                //是否是无条件差额计息
//                List<ProductInfoResDTO> paramProductInfoList = productInfoService.findProductInfo(accountManagementInfoDTO.getOrganizationNumber(),
//                        accountManagementInfoDTO.getProductNumber(), accountManagementInfoDTO.getCurrency());
//                String statementProcessingTableId = paramProductInfoList.get(0).getStatementProcessingTableId();
//                StatementProcessResDTO paramStatementProcess = statementProcessService.findByOrgAndTableId(accountManagementInfoDTO.getOrganizationNumber(), statementProcessingTableId);
//                String interestCalculateOption = paramStatementProcess.getInterestCalculateOption();
//                if(InterestCalculateOption.PART_ONE.getCode().equals(interestCalculateOption)){
//                    //无条件差额计息
//                    unconditionDiffRepaymentBackInterest(accountBalanceInfo,accountManagementInfoDTO,paramInterest,recordedBo.recorded,balance,interestProcessResultBO);
//                    return interestProcessResultBO;
//                }
//            }

            //全额计息
            TransactionCodeResDTO transactionCodeResDTO = recordedMidBo.transactionCodeResDTO;
            if (transactionCodeResDTO == null) {
                transactionCodeResDTO = getParamTransactionCode(accountBalanceInfo, recordedMidBo.recorded);
            }
            InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
            //开始利息回算
            logger.info("Calling interestAccureInnerService.backInterest, accountId={}", accountBalanceInfo.getTransactionBalanceId());
            InterestCalcResultBO backInterest = interestAccureInnerService.backInterest(
                    buildInterestParams(accountBalanceInfo, accountManagementInfoDTO, recordedMidBo.recorded, balance)
                    , interestLogHistoryDto, paramAccureInterest, transactionCodeResDTO, recordedMidBo.recorded);
            logger.info("interestAccureInnerService.backInterest call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
            if (backInterest != null && backInterest != InterestCalcResultBO.ZERO) {
                recordInterestHistory(accountBalanceInfo, accountManagementInfoDTO, interestProcessResultBO, interestLogHistoryDto
                        , backInterest);

            }
        }
        return interestProcessResultBO;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public InterestProcessResultBO accureAndBackInterestForCredit(AccountBalanceInfoDTO accountBalanceInfo,
                                                                  AccountManagementInfoDTO accountManagementInfo,
                                                                  RecordedMidBO recordedMidBO, BigDecimal paymentAmt,
                                                                  boolean sameTransactionType, CustReconciliationControlDTO controlDTO) {

        if (accountBalanceInfo.getInterestStartDate() == null) {
            return null;
        }

        LocalDate lastStatementDate = accountManagementInfo.getLastStatementDate();
        if (lastStatementDate == null || TransactionConstants.INITIAL_LOCALDATE.compareTo(lastStatementDate) == 0) {
            return accureAndBackInterestForRepayment(accountBalanceInfo, accountManagementInfo, recordedMidBO, paymentAmt);
        }

        LocalDate creditAdjustInterestStartDate = getCreditAdjustInterestStartDate(recordedMidBO, accountBalanceInfo);
        if (creditAdjustInterestStartDate.compareTo(lastStatementDate) > 0) {
            return accureAndBackInterestForRepayment(accountBalanceInfo, accountManagementInfo, recordedMidBO, paymentAmt);
        }
        //计算往期回算利息
//        return calculatePreviousInterest(recordedBO, accountManagementInfo, accountBalanceInfo,controlDTO,creditAdjustInterestStartDate,paymentAmt);
        // 大莱要求往期贷调不进行利息回算 ********
        return null;
    }

    /**
     * 计算贷调情况的起息日，原起息日和原始交易日按照晚的算
     *
     * @return
     */
    private LocalDate getCreditAdjustInterestStartDate(RecordedMidBO recordedMidBO, AccountBalanceInfoDTO accountBalanceInfo) {
        if (recordedMidBO.recorded.getTxnOriginalTransactionDate() != null) {
            LocalDate txnOriginalTransactionDate = recordedMidBO.recorded.getTxnOriginalTransactionDate().toLocalDate();
            if (txnOriginalTransactionDate.isAfter(accountBalanceInfo.getInterestStartDate())) {
                return txnOriginalTransactionDate;
            }
        }
        return accountBalanceInfo.getInterestStartDate();
    }

    /**
     * 计算往期回算利息
     * 2019年12月，光大POC追加功能
     * 【业务场景】 客户在账单日后发生消费/取现退货，此时上个账单日入账利息已产生，需要插入一条冲正流水进行抵消。
     * 同时，用户在账单日后到当前处理日的利息，也需要减去退款金额产生的利息
     */
    private InterestProcessResultBO calculatePreviousInterest(RecordedMidBO recordedMidBO,
                                                              AccountManagementInfoDTO accountManagementInfo,
                                                              AccountBalanceInfoDTO accountBalanceInfo, CustReconciliationControlDTO controlDTO,
                                                              LocalDate creditAdjustInterestStartDate, BigDecimal paymentAmt) {

        InterestBearingDTO paramAccureInterest = getParamAccureInterest(accountBalanceInfo);
        if (paramAccureInterest == null) {
            return null;
        }
        InterestSettlementDTO paramSettleInterest = getParamSettleInterest(accountBalanceInfo);
        if (paramSettleInterest == null) {
            return null;
        }
        /*//差额计息
        if(!getFullInterest(accountBalanceInfo,accountManagementInfo,paramInterest)){
            return creditSegmentedInterest(recorded,accountManagementInfo,accountBalanceInfo,paramInterest,controlDTO);
        }*/
        //全额计息
        InterestProcessResultBO interestProcessResultBO = new InterestProcessResultBO();
        InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
        //回算本金
        BigDecimal previousPrincipal = paymentAmt;
        //回算天数
        Long previousDays;
        Long previousDays1 = accountManagementInfo.getLastStatementDate().toEpochDay() -
                creditAdjustInterestStartDate.toEpochDay() + 1;

        Long interestBackDateDays = paramAccureInterest.getInterestBackdateMaxDays().longValue();
        if (previousDays1.compareTo(interestBackDateDays) > 0) {
            previousDays = interestBackDateDays;
        } else {
            previousDays = previousDays1;
        }
        //日利率
//        BigDecimal interestRate = interestAccureCalcService.calcDailyInterestRate(paramInterest);
        BigDecimal interestRate = accountBalanceInfo.getCurrentEffectInterestRate();
        //本次回算利息
        BigDecimal previousInterest = previousPrincipal.multiply(BigDecimal.valueOf(previousDays)).multiply(interestRate).setScale(2, BigDecimal.ROUND_HALF_UP);

        //step1 计算第一段（原始交易日期~上一账单日）之间产生的利息
        //无免息期
        if (GraceOptionEnum.NO.getCode().equals(paramSettleInterest.getGraceOption())) {
            if (accountBalanceInfo.getLastCycleBillInterest().compareTo(BigDecimal.ZERO) > 0) {
                processInterest(previousInterest, accountBalanceInfo, accountManagementInfo, paramSettleInterest, recordedMidBO, controlDTO);
            }
        } else {
            //有免息期
            if (accountBalanceInfo.getLastCycleBillInterest() != null && accountBalanceInfo.getLastCycleBillInterest().compareTo(BigDecimal.ZERO) > 0) {
                processInterest(previousInterest, accountBalanceInfo, accountManagementInfo, paramSettleInterest, recordedMidBO, controlDTO);
            } else {
                //只过了一个账单日，还未结息，金额写在累积系列上
                updateAcctBalanceAndProcessInterestHis(interestLogHistoryDto, accountBalanceInfo, previousInterest, previousDays, accountManagementInfo, paymentAmt);
            }
        }

        //step2 计算第二段 计算当期回算利息（上一账单日后一天~当前处理日前一天）
        Long previousDays2 = recordedMidBO.recorded.getTxnBillingDate().toEpochDay()
                - accountManagementInfo.getLastStatementDate().toEpochDay() - 1;
        BigDecimal previousInterest2 = previousPrincipal.multiply(BigDecimal.valueOf(previousDays2)).multiply(interestRate);
        if (previousInterest2.compareTo(BigDecimal.ZERO) > 0) {
            //更新原交易账户的累积利息、利息历史处理
            updateAcctBalanceAndProcessInterestHis(interestLogHistoryDto, accountBalanceInfo, previousInterest2, previousDays2, accountManagementInfo, paymentAmt);
        }

        interestProcessResultBO = buildInterestProcessResultBO(accountBalanceInfo, interestLogHistoryDto);

        return interestProcessResultBO;
    }

    //
    /*private InterestProcessResultBO creditSegmentedInterest(Recorded recorded, AccountManagementInfoDTO accountManagementInfo,
                                       AccountBalanceInfoDTO accountBalanceInfo,InterestResDTO paramInterest,CustReconciliationControlDTO controlDTO){
        InterestProcessResultBO interestProcessResultBO = new InterestProcessResultBO();
        InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
        //无免息期 不回算
        if ("0".equals(paramInterest.getGraceOption())) {
            return null;
        }else {
            //有免息期
            //回算本金
            BigDecimal previousPrincipal = recorded.getTxnBillingAmount();

            List<SegmentedInterestBO> dailyInterestList = calculateDailyInterstDuringDate(accountBalanceInfo,paramInterest, accountBalanceInfo.getInterestStartDate(), accountManagementInfo.getLastStatementDate());

            for (SegmentedInterestBO segmentedInterest : dailyInterestList) {
                LocalDate startDate = segmentedInterest.getStartDate();
                LocalDate endDate = segmentedInterest.getEndDate();
                BigDecimal dailyInterest = segmentedInterest.getDailyInterest();
                InterestResDTO interestResDTO = segmentedInterest.getInterestResDTO();
                //回算天数
                Long previousDays1 = endDate.toEpochDay() - startDate.toEpochDay();
                //本次回算利息
                BigDecimal previousInterest = previousPrincipal.multiply(BigDecimal.valueOf(previousDays1)).multiply(dailyInterest);
                if ( accountBalanceInfo.getLastCycleBillInterest()!=null  && accountBalanceInfo.getLastCycleBillInterest().compareTo(BigDecimal.ZERO) > 0) {
                    processInterest(previousInterest, accountBalanceInfo, accountManagementInfo, ,recorded,controlDTO);
                } else {
                    //只过了一个账单日，还未结息，金额写在累积系列上
                    updateAcctBalanceAndProcessInterestHis(interestLogHistoryDto, accountBalanceInfo, previousInterest, previousDays1, recorded, accountManagementInfo,interestResDTO);
                }
            }

            //step2 计算第二段 计算当期回算利息（上一账单日后一天~当前处理日前一天）
            List<SegmentedInterestBO> dailyInterestList2 = calculateDailyInterstDuringDate(accountBalanceInfo,paramInterest, accountManagementInfo.getLastStatementDate().plusDays(1), recorded.getTxnBillingDate().minusDays(1));
            for (SegmentedInterestBO segmentedInterest : dailyInterestList2) {
                LocalDate startDate = segmentedInterest.getStartDate();
                LocalDate endDate = segmentedInterest.getEndDate();
                BigDecimal dailyInterest = segmentedInterest.getDailyInterest();
                InterestResDTO interestResDTO = segmentedInterest.getInterestResDTO();
                //回算天数
                Long previousDays2 = endDate.toEpochDay() - startDate.toEpochDay();
                BigDecimal previousInterest2 = previousPrincipal.multiply(BigDecimal.valueOf(previousDays2)).multiply(dailyInterest);
                if(previousInterest2.compareTo(BigDecimal.ZERO)>0){
                    //更新原交易账户的累积利息、利息历史处理
                    updateAcctBalanceAndProcessInterestHis(interestLogHistoryDto, accountBalanceInfo, previousInterest2, previousDays2, recorded, accountManagementInfo,interestResDTO);
                }

                interestProcessResultBO = buildInterestProcessResultBO(accountBalanceInfo, interestLogHistoryDto);
            }

            return interestProcessResultBO;
        }
    }*/

    /*@Override
    public List<SegmentedInterestBO> calculateDailyInterstDuringDate(AccountBalanceInfoDTO accountBalanceInfo,InterestResDTO paramInterest,LocalDate startDate,LocalDate endDate){
        List<SegmentedInterestBO> interestList = new ArrayList<>();
        String orgNum = paramInterest.getOrganizationNumber();
        String interestTableId = paramInterest.getTableId();
        List<PmInterestHis> pmInterestHis = pmInterestHisSelfMapper.selectByOrgInterestTidAndDate(orgNum, interestTableId,startDate, endDate);
        if(CollectionUtils.isEmpty(pmInterestHis)){
            log.warn("机构：{}的利率表：{}没有利率历史",orgNum,interestTableId);
//            BigDecimal dailyInterest = interestAccureCalcService.calcDailyInterestRate(paramInterest);
            BigDecimal dailyInterest = accountBalanceInfo.getCurrentEffectInterestRate();
            SegmentedInterestBO segmentedInterestBO = new SegmentedInterestBO(startDate,endDate,dailyInterest,paramInterest);
            interestList.add(segmentedInterestBO);
            return interestList;
        }
        List<PmInterestHis> distinctInterestHisList = pmInterestHis.stream().filter(distinctByKey(PmInterestHis::getOperateDate)).sorted(Comparator.comparing(PmInterestHis :: getOperateDate)).collect(Collectors.toList());

        if(distinctInterestHisList.get(0).getOperateDate().isAfter(startDate)){
            PmInterestHis startInterestHis = pmInterestHisSelfMapper.selectStartByOrgInterestTidAndDate(orgNum, interestTableId,startDate);
            if(null == startInterestHis){
                PmInterestHis interestHis = new PmInterestHis();
                interestHis.setOperateDate(startDate);
                interestHis.setJsonValue(JSON.toJSONString(paramInterest));
                distinctInterestHisList.add(0,interestHis);
            }else {
                distinctInterestHisList.add(0,startInterestHis);
            }

        }

        for (int i = 0; i < distinctInterestHisList.size(); i++) {
            PmInterestHis interestHis = distinctInterestHisList.get(i);
            LocalDate interestStartDate;
            LocalDate interestEndDate;
            if (i == 0) {
                interestStartDate = startDate;
            } else {
                interestStartDate = interestHis.getOperateDate();
            }

            if (i == distinctInterestHisList.size() - 1) {
                interestEndDate = endDate;
            } else {
                interestEndDate = distinctInterestHisList.get(i + 1).getOperateDate();
            }
            if(interestStartDate.compareTo(interestEndDate) != 0){
                InterestResDTO interestResDTO = JSONObject.parseObject(interestHis.getJsonValue(), InterestResDTO.class);
//                BigDecimal dailyInterest = interestAccureCalcService.calcDailyInterestRate(interestResDTO);
                BigDecimal dailyInterest = accountBalanceInfo.getCurrentEffectInterestRate();
                SegmentedInterestBO segmentedInterestBO = new SegmentedInterestBO(interestStartDate,interestEndDate,dailyInterest,interestResDTO);
                interestList.add(segmentedInterestBO);
            }

        }
        return interestList;
    }*/

    /**
     * 利息处理结果
     */
    private InterestProcessResultBO buildInterestProcessResultBO(AccountBalanceInfoDTO accountBalanceInfo,
                                                                 InterestLogHistoryDTO interestLogHistoryDto) {
        InterestProcessResultBO interestProcessResultBO = new InterestProcessResultBO();
        interestProcessResultBO.setInterestAccrueDate(accountBalanceInfo.getInterestAccrueDate());
        interestProcessResultBO.setLastInterestAccrueDate(accountBalanceInfo.getLastInterestAccrueDate());
        interestProcessResultBO.setAccrueInterest(accountBalanceInfo.getAccrueInterest());
        interestProcessResultBO.setInterestLogHistory(interestLogHistoryDto);
        interestProcessResultBO.setLastCycleBillInterest(accountBalanceInfo.getLastCycleBillInterest());
        return interestProcessResultBO;
    }

    /**
     * 更新原交易账户的累积利息、利息历史处理
     */
    private void updateAcctBalanceAndProcessInterestHis(InterestLogHistoryDTO interestLogHistoryDto,
                                                        AccountBalanceInfoDTO accountBalanceInfo,
                                                        BigDecimal previousInterest,
                                                        Long previousDays,
                                                        AccountManagementInfoDTO accountManagementInfo, BigDecimal paymentAmt) {
        //更新前的累积利息
        BigDecimal interestBeforeUpdate = accountBalanceInfo.getAccrueInterest();
        accountBalanceInfo.setAccrueInterest(accountBalanceInfo.getAccrueInterest().
                subtract(previousInterest).compareTo(BigDecimal.ZERO) < 0
                ? BigDecimal.ZERO
                : accountBalanceInfo.getAccrueInterest().subtract(previousInterest));
        //更新原交易账户的累积利息
        updateAccountBalanceInfo(accountBalanceInfo);
        //利息历史处理
        interestHistoryProcess(interestLogHistoryDto, interestBeforeUpdate, accountBalanceInfo, previousDays, accountManagementInfo, paymentAmt);
    }

    /**
     * 利息处理
     */
    private void processInterest(BigDecimal previousInterest,
                                 AccountBalanceInfoDTO accountBalanceInfo,
                                 AccountManagementInfoDTO accountManagementInfo,
                                 InterestSettlementDTO interestSettlementDTO,
                                 RecordedMidBO parentRecordedMidBO, CustReconciliationControlDTO controlDTO) {
        if (previousInterest.compareTo(accountBalanceInfo.getLastCycleBillInterest()) > 0) {
            previousInterest = accountBalanceInfo.getLastCycleBillInterest();
        }
        //编辑利息下账接口
        RecordedBO record = getInterestRecord(accountManagementInfo, interestSettlementDTO, accountBalanceInfo, previousInterest, parentRecordedMidBO.recorded);
        //调用核心入账逻辑 TODO 0利息不调入账 update by ********
        if (record.getTxnBillingAmount().compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        parentRecordedMidBO.getSelfRecordedList().add(record);
        accountBalanceInfo.setLastCycleBillInterest(accountBalanceInfo.getLastCycleBillInterest().
                subtract(previousInterest).compareTo(BigDecimal.ZERO) < 0
                ? BigDecimal.ZERO
                : accountBalanceInfo.getLastCycleBillInterest().subtract(previousInterest));
        updateAccountBalanceInfo(accountBalanceInfo);
    }

    /**
     * 利息历史处理
     */
    private void interestHistoryProcess(InterestLogHistoryDTO interestLogHistoryDto,
                                        BigDecimal interestBeforeUpdate,
                                        AccountBalanceInfoDTO accountBalanceInfo,
                                        Long previousDays,
                                        AccountManagementInfoDTO accountManagementInfo, BigDecimal paymentAmt) {
        interestLogHistoryDto.setProssingIndicator(InterestProssingIndicatorEnum.TWO.getCode());
        interestLogHistoryDto.setAccrueInterestBeforeUpdate(interestBeforeUpdate);
        interestLogHistoryDto.setAccrueInterestAfterUpdate(accountBalanceInfo.getAccrueInterest());
        interestLogHistoryDto.setCurrentAccrueDays(previousDays);
        interestLogHistoryDto.setCurrentAccrueBalance(paymentAmt);

        interestLogHistoryDto.setCurrentInterestRate(accountBalanceInfo.getCurrentEffectInterestRate());


        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

        String flag = organizationInfo.getInterestHistoryFlag();
        if ("1".equals(flag)) {
            interestAccureInnerService.createInterestLogHistory(accountBalanceInfo,
                    accountManagementInfo,
                    interestLogHistoryDto);
        }

    }

    /**
     * 更新交易账户
     *
     * @param accountBalanceInfoDTO 交易账户参数
     */
    private void updateAccountBalanceInfo(AccountBalanceInfoDTO accountBalanceInfoDTO) {
        AccountBalanceInfo accountBalanceInfo = BeanMapping.copy(accountBalanceInfoDTO, AccountBalanceInfo.class);
        accountBalanceInfo.setUpdateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
        try {
            txnRecordedCacheAccountBalanceService.updateByPrimaryKey(accountBalanceInfo);
//            if(1!=i){
//                LOG.error("根据主键{},更新表{}失败,期望影响条数：{},实际影响条数：{},",accountBalanceInfoDTO.getTransactionBalanceId(),"Account_Balance_Info",1,i);
//                throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_DATABASE_UPDATE_EFFECT_NUM_ERROR);
//            }
//            accountBalanceInfoDTO.setVersionNumber(accountBalanceInfoDTO.getVersionNumber()+1);
        } catch (Exception e) {
            logger.error("Update transaction account failed, accountId={}", accountBalanceInfoDTO.getTransactionBalanceId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, e);
        }
    }

    /**
     * 编辑利息下账接口
     */
    private RecordedBO getInterestRecord(AccountManagementInfoDTO accountManagementInfo,
                                         InterestSettlementDTO interestSettlementDTO,
                                         AccountBalanceInfoDTO accountBalanceInfo,
                                         BigDecimal previousInterest,
                                         RecordedBO parentRecorded) {
        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
        String productNumber = accountManagementInfo.getProductNumber();
        List<ParmCardProductInfo> parmCardProductInfos = cardProductInfoSelfMapper.selectByOrgAndAccountProductNum(OrgNumberUtils.getOrg(), productNumber);
        if (parmCardProductInfos == null || parmCardProductInfos.size() == 0) {
            logger.error("No card product parameters found for organization number: {}, product number: {}", accountManagementInfo.getOrganizationNumber(), productNumber);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfos.get(0);
        String cardProductNumber = parmCardProductInfo.getProductNumber();
        RecordedBO recorded = new RecordedBO();
        for (CardAuthorizationInfo cardAuthorizationInfo : cardAuthorizationInfos) {
            if (cardProductNumber.equals(cardAuthorizationInfo.getProductNumber()) && cardAuthorizationInfo.getRelationshipIndicator().equals("P")) {
                recorded.setTxnCardNumber(cardAuthorizationInfo.getCardNumber());
            }
        }
        recorded.setCustomerId(accountManagementInfo.getCustomerId());
        recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
        recorded.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        recorded.setTxnPostMethod("0");
        recorded.setTxnRepostFromSuspend("0");
        recorded.setTxnReverseFeeIndicator("1");
        if (GraceOptionEnum.NO.getCode().equals(interestSettlementDTO.getGraceOption())) {
            List<TransactionCodeResDTO> transactionCodeResDtos = transactionCodeService.selectByTransAttrAndDebitCreditInd("B", "C");
            recorded.setTxnTransactionCode(transactionCodeResDtos.get(0).getTransactionCode());
        }
        if (GraceOptionEnum.HAVE.getCode().equals(interestSettlementDTO.getGraceOption())) {
            List<TransactionCodeResDTO> transactionCodeResDtos = transactionCodeService.selectByTransAttrAndDebitCreditInd("A", "C").stream().sorted(Comparator.comparing(TransactionCodeResDTO::getTransactionCode)).collect(Collectors.toList());
            recorded.setTxnTransactionCode(transactionCodeResDtos.get(0).getTransactionCode());
        }
        recorded.setTxnTransactionSource("0");
        TransactionCodeResDTO transactionCodeResDTO = getParamTransactionCode(accountBalanceInfo, recorded);
        recorded.setTxnTransactionDescription(transactionCodeResDTO.getDescription());
        // 入账日期 and 交易日期由入账逻辑赋值
        recorded.setTxnTransactionAmount(previousInterest);
        recorded.setTxnTransactionCurrency(accountManagementInfo.getCurrency());

        recorded.setTxnBillingAmount(previousInterest);
        recorded.setTxnBillingCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnSettlementAmount(previousInterest);
        recorded.setTxnSettlementCurrency(accountManagementInfo.getCurrency());
        recorded.setTxnAuthorizationMatchIndicator("0");
        recorded.setTxnReleaseAuthorizationAmount("N");
        recorded.setTxnForcePostIndicator("0");
        //给原交易id赋值
        recorded.setTxnOriginalPostedTransactionId(parentRecorded.getTxnOriginalPostedTransactionId());

        //管控单元获取
//        TransBaseInfoDTO transBaseInfoDTO = new TransBaseInfoDTO();
//        if("0".equals(interestSettlementDTO.getLmtUnitCodeFollowIndicator())){
//            //调用获取管控单元接口
//            transBaseInfoDTO.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());
//            transBaseInfoDTO.setCustomerId(accountManagementInfo.getCustomerId());
//            transBaseInfoDTO.setAccountManagementId(accountBalanceInfo.getAccountManagementId());
//            transBaseInfoDTO.setAccountProductCode(accountManagementInfo.getProductNumber());
//            transBaseInfoDTO.setPostTxnCode(recorded.getTxnTransactionCode());
////            transBaseInfoDTO.setPostTxnType(transactionCodeResDTO.getTransactionTypeCode());
//            transBaseInfoDTO.setAccountCurrency(recorded.getTxnBillingCurrency());
//            LimitMatchResultBO limitMatchResult = limitMatchCheckService.matchLimitUnit(transBaseInfoDTO);
//            //查询管控单元编号
//            LimitUnitDefi limitUnitDefi = limitUnitDefiMapper.selectByPrimaryKey(limitMatchResult.getLimitUnitId());
//            recorded.setLimitUnitCode(limitUnitDefi.getUnitCode());
//            recorded.setLimitUnitVersion(String.valueOf(limitUnitDefi.getActiveVersion()));
//        }
//        if("1".equals(interestSettlementDTO.getLmtUnitCodeFollowIndicator())){
//            recorded.setLimitUnitCode(accountBalanceInfo.getLimitUnitCode());
//            recorded.setLimitUnitVersion(String.valueOf(accountBalanceInfo.getLimitUnitVersion()));
//        }
        //单双信息赋值
        recorded.setMessageIndicator(MessageIndicatorEnum.OTHER_INDICATOR.getCode());
        return recorded;
    }

    /**
     * 利息回算
     */
    private InterestProcessResultBO backInterestCalculate(AccountBalanceInfoDTO accountBalanceInfo,
                                                          AccountManagementInfoDTO accountManagementInfo,
                                                          RecordedBO recorded, BigDecimal balance,
                                                          boolean sameTransactionType) {
        InterestProcessResultBO interestProcessResultBO = null;
        if (StringUtils.isNotBlank(accountBalanceInfo.getInterestAccrualTableId())) {
            InterestBearingDTO paramAccureInterest = getParamAccureInterest(accountBalanceInfo);
            OrganizationInfoResDTO paramOrganizationInfo = getParamOrganizationInfo(accountBalanceInfo);
            if (InterestAccrualMethodEnum.EVENT_ACCRUAL.getCode().equals(paramOrganizationInfo.getInterestAccrualMethod())) {
                Boolean calculateAccrueInterestFlag = beforeCalculateAccrueInterest(accountBalanceInfo, accountManagementInfo);
                if (!calculateAccrueInterestFlag) {
                    return null;
                }
                logger.info("Calling interestAccureInnerService.accureInterest, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                interestProcessResultBO = interestAccureInnerService.accureInterest(accountBalanceInfo
                        , accountManagementInfo, recorded);
                logger.info("interestAccureInnerService.accureInterest call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
            } else {
                interestProcessResultBO = new InterestProcessResultBO(
                        accountBalanceInfo.getInterestAccrueDate(),
                        accountBalanceInfo.getLastInterestAccrueDate(),
                        accountBalanceInfo.getAccrueInterest());
            }

            //全额计息进行利息回算，差额计息不回算利息
//            if(getFullInterest(accountBalanceInfo,accountManagementInfo,paramInterest)){
            InterestParamsBO interestParamsBO = buildInterestParams(accountBalanceInfo, accountManagementInfo, recorded, balance);
            interestParamsBO.setSameTransactionType(sameTransactionType);
            TransactionCodeResDTO transactionCodeResDTO = getParamTransactionCode(accountBalanceInfo, recorded);
            InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
            InterestCalcResultBO backInterest = interestAccureInnerService.backInterest(interestParamsBO
                    , interestLogHistoryDto, paramAccureInterest, transactionCodeResDTO, recorded);
            if (backInterest != null) {
                recordInterestHistory(accountBalanceInfo, accountManagementInfo, interestProcessResultBO,
                        interestLogHistoryDto, backInterest);
            }
//            }
        }
        // 更新累积利息相关字段
        return interestProcessResultBO;
    }

    /**
     * 记录利息历史
     *
     * @param accountBalanceInfo      交易账户信息
     * @param accountManagementInfo   管理账户信息
     * @param interestProcessResultBO 利息处理结果
     * @param interestLogHistoryDto   利息历史记录
     * @param backInterest            回算利息
     */
    private void recordInterestHistory(AccountBalanceInfoDTO accountBalanceInfo,
                                       AccountManagementInfoDTO accountManagementInfo,
                                       InterestProcessResultBO interestProcessResultBO,
                                       InterestLogHistoryDTO interestLogHistoryDto,
                                       InterestCalcResultBO backInterest) {
        backInterest.setAccrueInterest(interestProcessResultBO.getAccrueInterest().subtract(
                backInterest.getAccrueInterest()));

        if (backInterest.getAccrueInterest().compareTo(BigDecimal.ZERO) < 0) {
            backInterest.setAccrueInterest(BigDecimal.ZERO);
        }
        backInterest.setAccrueInterest(
                backInterest.getAccrueInterest().setScale(6, BigDecimal.ROUND_HALF_UP));

        interestLogHistoryDto.setAccrueInterestBeforeUpdate(
                interestProcessResultBO.getAccrueInterest());
        interestLogHistoryDto.setAccrueInterestAfterUpdate(backInterest.getAccrueInterest());
        interestLogHistoryDto.setCurrentAccrueDays(backInterest.getAccrueDays());
        interestLogHistoryDto.setCurrentAccrueBalance(backInterest.getAccrueBalance());
        if (backInterest.getInterestStartDate() != null) {
            interestLogHistoryDto.setAccrueDate(backInterest.getInterestStartDate());
        }
        interestProcessResultBO.setAccrueInterest(backInterest.getAccrueInterest());
        logger.info("Calling organizationInfoService.findOrganizationInfo, accountId={}", accountBalanceInfo.getTransactionBalanceId());
        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("organizationInfoService.findOrganizationInfo call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());

        String flag = organizationInfo.getInterestHistoryFlag();
        if ("1".equals(flag)) {
            //贷记交易 更新前和更新后 如果都是0 不再插入利息历史表中
            if (interestLogHistoryDto.getAccrueInterestBeforeUpdate().compareTo(new BigDecimal(0)) == 0 &&
                    interestLogHistoryDto.getAccrueInterestBeforeUpdate().compareTo(interestLogHistoryDto.getAccrueInterestAfterUpdate()) == 0) {
                return;
            }
            interestAccureInnerService.createInterestLogHistory(accountBalanceInfo,
                    accountManagementInfo,
                    interestLogHistoryDto);
        }


    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public void backInterest(AccountBalanceInfoDTO accountBalanceInfo,
                             AccountManagementInfoDTO accountManagementInfo,
                             RecordedBO recorded, BigDecimal balance) {
        logger.info("Calling interestAccureInnerService.backInterest, accountId={}", accountBalanceInfo.getTransactionBalanceId());
        if (StringUtils.isNotBlank(accountBalanceInfo.getInterestAccrualTableId())) {
            InterestBearingDTO paramAccureInterest = getParamAccureInterest(accountBalanceInfo);
            TransactionCodeResDTO transactionCodeResDTO = getParamTransactionCode(accountBalanceInfo, recorded);
            InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
            Boolean calculateAccrueInterestFlag = beforeCalculateAccrueInterest(accountBalanceInfo, accountManagementInfo);
            if (!calculateAccrueInterestFlag) {
                return;
            }
            InterestCalcResultBO backInterest = interestAccureInnerService.backInterest(
                    buildInterestParams(accountBalanceInfo, accountManagementInfo, recorded, balance),
                    interestLogHistoryDto, paramAccureInterest, transactionCodeResDTO, recorded);

            if (backInterest != null) {
                BigDecimal accrueInterest = accountBalanceInfo.getAccrueInterest().add(
                        backInterest.getAccrueInterest()).setScale(6,
                        BigDecimal.ROUND_HALF_UP);
                logger.info("interestAccureInnerService.backInterest call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                InterestProcessResultBO interestProcessResultBO = new InterestProcessResultBO();
                interestProcessResultBO.setAccrueInterest(accrueInterest);
                interestProcessResultBO.setInterestAccrueDate(recorded.getTxnBillingDate().minusDays(1));
                interestProcessResultBO.setLastInterestAccrueDate(
                        accountBalanceInfo.getInterestAccrueDate());
                interestLogHistoryDto.setAccrueInterestBeforeUpdate(
                        accountBalanceInfo.getAccrueInterest());
                // 更新累积利息相关字段
                updateInterest(interestProcessResultBO, accountBalanceInfo);
                interestLogHistoryDto.setAccrueInterestAfterUpdate(
                        interestProcessResultBO.getAccrueInterest());
                interestLogHistoryDto.setCurrentAccrueDays(backInterest.getAccrueDays());
                interestLogHistoryDto.setCurrentAccrueBalance(
                        backInterest.getAccrueBalance());


                OrganizationInfoResDTO organizationInfo =
                        organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());

                String flag = organizationInfo.getInterestHistoryFlag();
                logger.info("The flag indicating whether to insert interest history is {}", flag);
                if ("1".equals(flag)) {
                    logger.info("Calling interestAccureInnerService.createInterestLogHistory, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                    interestAccureInnerService.createInterestLogHistory(accountBalanceInfo, accountManagementInfo, interestLogHistoryDto);
                    logger.info("interestAccureInnerService.createInterestLogHistory call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                }


            }
        }


    }

//    @Override
//    public InterestProcessResultBO accureInterest(AccountBalanceInfoDTO accountBalanceInfo,
//                                                  LocalDate accruedThruDay,
//                                                  boolean fullInterest,
//                                                  InterestResDTO paramInterest) {
//        LOG.info("利息处理，账户交易余额信息ID: [{}]", accountBalanceInfo.getTransactionBalanceId());
//        return interestAccureInnerService.accureInterest(accountBalanceInfo,
//                accruedThruDay, fullInterest,
//                paramInterest);
//    }

    @Override
    public InterestLogHistoryDTO createInterestLogHistoryDtoForBatch(AccountBalanceInfoDTO accountBalanceInfo,
                                                                     AccountManagementInfoDTO accountManagementInfo,
                                                                     InterestProcessResultBO interestProcessResultBO,
                                                                     BatchInterestEnum flag) {
        InterestLogHistoryDTO interestLogHistoryDTO = null;
        switch (flag.getCode()) {
            case "1":
                interestLogHistoryDTO = interestProcessResultBO.getInterestLogHistory();
                break;
            case "2":
                interestLogHistoryDTO = buildInterestLogHistory();
                interestLogHistoryDTO.setProssingIndicator(
                        InterestProssingIndicatorEnum.FOUR.getCode());
                interestLogHistoryDTO.setAccrueInterestBeforeUpdate(
                        accountBalanceInfo.getAccrueInterest());
                break;
            case "3":
                interestLogHistoryDTO = buildInterestLogHistory();
                interestLogHistoryDTO.setProssingIndicator(
                        InterestProssingIndicatorEnum.FIVE.getCode());
                interestLogHistoryDTO.setAccrueInterestBeforeUpdate(
                        accountBalanceInfo.getAccrueInterest());
                break;
            default:
        }
        if (interestLogHistoryDTO != null) {
            logger.info("Calling interestAccureInnerService.createInterestLogHistoryCommonProperty, accountId={}", accountBalanceInfo.getTransactionBalanceId());
            interestAccureInnerService.createInterestLogHistoryCommonProperty(accountBalanceInfo, accountManagementInfo, interestLogHistoryDTO);
            logger.info("interestAccureInnerService.createInterestLogHistoryCommonProperty call completed, accountId={}", accountBalanceInfo.getTransactionBalanceId());
        }
        return interestLogHistoryDTO;
    }


    /**
     * 构建利息更新流水表
     *
     * @return InterestLogHistory
     */
    private InterestLogHistoryDTO buildInterestLogHistory() {
        InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
        interestLogHistoryDto.setCurrentAccrueBalance(BigDecimal.ZERO);
        interestLogHistoryDto.setCurrentAccrueDays(0L);
        interestLogHistoryDto.setAccrueInterestAfterUpdate(BigDecimal.ZERO);
        return interestLogHistoryDto;
    }

    /**
     * 根据利息处理结果更新交易账户的利息信息
     *
     * @param interestProcessResultBO 利息处理结果
     * @param accountBalanceInfo      交易账户
     */
    private void updateInterest(InterestProcessResultBO interestProcessResultBO,
                                AccountBalanceInfoDTO accountBalanceInfo) {
        AccountBalanceInfo accountBalanceInfoUpdate = txnRecordedCacheAccountBalanceService.selectByPrimaryKey(accountBalanceInfo.getTransactionBalanceId());
        accountBalanceInfoUpdate.setAccrueInterest(
                interestProcessResultBO.getAccrueInterest());
        accountBalanceInfoUpdate.setInterestAccrueDate(
                interestProcessResultBO.getInterestAccrueDate());
        accountBalanceInfoUpdate.setLastInterestAccrueDate(
                interestProcessResultBO.getLastInterestAccrueDate());
        try {

            txnRecordedCacheAccountBalanceService.updateByPrimaryKey(accountBalanceInfoUpdate);

        } catch (Exception e) {
            logger.error("Failed to call [{}] to update the database table [{}], error message [{}]", "updateByPrimaryKeySelective", "ACCOUNT_BALANCE_INFO", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, e);

        }
        accountBalanceInfo.setAccrueInterest(
                accountBalanceInfoUpdate.getAccrueInterest());
        accountBalanceInfo.setInterestAccrueDate(
                accountBalanceInfoUpdate.getInterestAccrueDate());
        accountBalanceInfo.setLastInterestAccrueDate(
                accountBalanceInfoUpdate.getLastInterestAccrueDate());
    }

    /**
     * 构造利息累积参数
     *
     * @param accountBalanceInfo    交易账户信息
     * @param accountManagementInfo 管理账户信息
     * @param recorded              接口数据
     * @param reducedBalance        余额
     * @return 利息累积参数
     */
    private InterestParamsBO buildInterestParams(AccountBalanceInfoDTO accountBalanceInfo,
                                                 AccountManagementInfoDTO accountManagementInfo,
                                                 RecordedBO recorded,
                                                 BigDecimal reducedBalance) {
        InterestParamsBO interestParamsBO = new InterestParamsBO();
        interestParamsBO.setOrganizationNumber(accountBalanceInfo.getOrganizationNumber());
        interestParamsBO.setTransactionInterestTableId(
                accountBalanceInfo.getInterestAccrualTableId());
        interestParamsBO.setAccrueInterest(accountBalanceInfo.getAccrueInterest());
        interestParamsBO.setInterestAccrueDate(accountBalanceInfo.getInterestAccrueDate());
        interestParamsBO.setBalance(accountBalanceInfo.getBalance());
        interestParamsBO.setInterestStartDate(accountBalanceInfo.getInterestStartDate());
        interestParamsBO.setLastStatementDate(
                getDateByParamIsNull(accountManagementInfo.getLastStatementDate()));
        interestParamsBO.setPreviousStatementDate(
                getDateByParamIsNull(accountManagementInfo.getPreviousStatementDate()));
        interestParamsBO.setReducedBalance(reducedBalance);
        interestParamsBO.setTxnBillingAmount(reducedBalance);
        interestParamsBO.setTxnTransactionDate(recorded.getTxnTransactionDate().toLocalDate());

        if (recorded.getTxnOriginalTransactionDate() != null) {
            interestParamsBO.setTxnOriginalTransactionDate(recorded.getTxnOriginalTransactionDate().toLocalDate());
        }
        interestParamsBO.setTxnBillingDate(recorded.getTxnBillingDate());
        interestParamsBO.setTxnTransactionCode(recorded.getTxnTransactionCode());
        interestParamsBO.setCurrentEffectInterestRate(accountBalanceInfo.getCurrentEffectInterestRate());
        return interestParamsBO;
    }

    /**
     * 传入的日期不为空时，返回该日期，否则返回日期为（1，1，1）
     *
     * @param date 日期
     * @return LocalDate
     */
    private LocalDate getDateByParamIsNull(LocalDate date) {
        return date == null ? TransactionConstants.INITIAL_LOCALDATE : date;
    }

    /**
     * 查询计息参数信息
     *
     * @param accountBalanceInfo 交易账户信息
     * @return 计息参数
     */
    private InterestBearingDTO getParamAccureInterest(AccountBalanceInfoDTO accountBalanceInfo) {
        if (StringUtils.isNotBlank(accountBalanceInfo.getInterestAccrualTableId())) {
            InterestBearingDTO interestBearingDTO = interestBearingService.findByOrgAndTableId(accountBalanceInfo.getOrganizationNumber(), accountBalanceInfo.getInterestAccrualTableId());
            if (interestBearingDTO == null) {
                logger.error("Interest bearing parameters do not exist, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            return interestBearingDTO;
        }
        return null;
    }

    /**
     * 查询结息参数信息
     *
     * @param accountBalanceInfo 交易账户信息
     * @return 结息参数
     */
    private InterestSettlementDTO getParamSettleInterest(AccountBalanceInfoDTO accountBalanceInfo) {
        if (StringUtils.isNotBlank(accountBalanceInfo.getInterestSettleTableId())) {
            InterestSettlementDTO interestSettlementDTO = interestSettlementService.findByOrgAndTableId(accountBalanceInfo.getOrganizationNumber(), accountBalanceInfo.getInterestSettleTableId());
            if (interestSettlementDTO == null) {
                logger.error("Interest settlement parameters do not exist, accountId={}", accountBalanceInfo.getTransactionBalanceId());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
            }
            return interestSettlementDTO;
        }
        return null;
    }

    /**
     * 获取交易码参数
     *
     * @param accountBalanceInfo 交易账户信息
     * @param recorded           接口数据
     * @return 交易码参数
     */
    private TransactionCodeResDTO getParamTransactionCode(AccountBalanceInfoDTO accountBalanceInfo,
                                                          RecordedBO recorded) {
        TransactionCodeResDTO transactionCodeDTO = transactionCodeService.findTransactionCode(accountBalanceInfo.getOrganizationNumber(), recorded.getTxnTransactionCode());
        if (transactionCodeDTO == null) {
            logger.error("transactionCodeDTO is null");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
        }
        return transactionCodeDTO;
    }

    /**
     * 获取机构参数表
     *
     * @param accountBalanceInfo 交易账户信息
     * @return
     */
    private OrganizationInfoResDTO getParamOrganizationInfo(
            AccountBalanceInfoDTO accountBalanceInfo) {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountBalanceInfo.getOrganizationNumber());
        if (organizationInfo == null) {
            logger.error("paramOrganizationInfo is null");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_ORG_NOT_EXIST);
        }

        return organizationInfo;
    }


    /**
     * 余额计息具体计算处理的前提条件
     *
     * @param balanceAccount       交易账户
     * @param managementAccountDTO 管理账户
     * @return Boolean
     */
    @Override
    public Boolean beforeCalculateAccrueInterest(AccountBalanceInfoDTO balanceAccount, AccountManagementInfoDTO managementAccountDTO) {
        List<ProductInfoResDTO> paramProductInfoList = productInfoService.findProductInfo(managementAccountDTO.getOrganizationNumber(),
                managementAccountDTO.getProductNumber(), managementAccountDTO.getCurrency());
        if (CollectionUtils.isEmpty(paramProductInfoList)) {
            logger.error("Query account product parameter table data is null! organization number: {}, product number: {}, currency: {}",
                    managementAccountDTO.getOrganizationNumber(), managementAccountDTO.getProductNumber(),
                    managementAccountDTO.getCurrency());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_PRODUCT_NOT_EXIST);
        }
        DelinquentControlResDTO delinquentControlResDTO = delinquentControlService.
                findByOrgNumTableIdCycleDue(managementAccountDTO.getOrganizationNumber(),
                        paramProductInfoList.get(0).getDelinquentControlTableId(),
                        Integer.valueOf(managementAccountDTO.getCycleDue()));
        if (delinquentControlResDTO == null) {
            logger.error("Query delinquency control parameter table data is null! organization number: {}, delinquency control parameter table id: {}, delinquency status: {}",
                    managementAccountDTO.getOrganizationNumber(), managementAccountDTO.getProductNumber(),
                    managementAccountDTO.getCycleDue());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_DELIN_CONTROL_NOT_EXIST);
        }
        TransactionTypeResDTO paramTransactionType = transactionTypeService.findTransactionType(balanceAccount.getOrganizationNumber(), balanceAccount.getTransactionTypeCode());
        if (paramTransactionType == null) {
            logger.error("Query transaction type parameter table data is null! organization number: {}, transaction type: {}",
                    balanceAccount.getOrganizationNumber(), balanceAccount.getTransactionTypeCode());
            return false;
        }
        // 交易类型标志 = 3：消费利息/4：现金利息 且 计收复利标志 != 0 不计息
        if ("3".equals(paramTransactionType.getFinanceFlag()) || "4".equals(paramTransactionType.getFinanceFlag())) {
            if (!"0".equals(delinquentControlResDTO.getInterestOnInterestOption())) {
                return false;
            }
        }
        return true;
    }


    /**
     * 构建利息历史参数数据
     *
     * @param logBOList                利息历史List
     * @param accountBalanceInfoDTO    交易账户
     * @param accountManagementInfoDTO 管理账户
     * @param interestProcessResult    利息累积结果
     */
    @Override
    public void buildLogArgs(List<InterestLogBO> logBOList, AccountBalanceInfoDTO accountBalanceInfoDTO, AccountManagementInfoDTO accountManagementInfoDTO, InterestProcessResultBO interestProcessResult, BatchInterestEnum flag) {
        InterestLogBO logBO = new InterestLogBO();
        logBO.setAccountBalanceInfo(accountBalanceInfoDTO);
        logBO.setAccountManagementInfo(accountManagementInfoDTO);
        logBO.setInterestProcessResult(interestProcessResult);
        logBO.setFlag(flag);
        logBOList.add(logBO);
    }

    /**
     * 获得账单账户信息的期末余额
     * 根据账户管理信息id（account_management_id），上一账单日（last_statement_date）读取账单账户信息
     *
     * @param managementAccount 管理账户信息
     * @return BigDecimal 期末余额
     */
    private BigDecimal getCloseBalance(AccountManagementInfo managementAccount) {
        return accountStatementInfoSelfMapper.getCloseBalance(managementAccount.getAccountManagementId(),
                managementAccount.getLastStatementDate());
    }


    /**
     * @param interestBos
     */
    public void write4InterestSettledBatch(List<? extends InterestBO> interestBos) {
        interestBos.forEach(interestBO -> {
            if (interestBO.getFreeInterestFlag() != null && interestBO.getFreeInterestFlag()) {
                //停止计息标志
                accountBalanceInfoSelfMapper.updateAccrueInterest4Free(
                        interestBO.getBalanceAccount().getTransactionBalanceId(),
                        interestBO.getBalanceAccount().getActualInterestBillingDate().plusMonths(1));
                //此两处会导致多次更新，需要放到循环外面。后续有必要再优化
                if (interestBO.getAccountManagementWaiveInterestUpdateFlag() != null &&
                        interestBO.getAccountManagementWaiveInterestUpdateFlag()) {
                    accountManagementInfoSelfMapper.updateWaiveInterestFlag(interestBO.getManagementAccount().getAccountManagementId());
                }
                if (interestBO.getCustomerBasicInfoWaiveInterestUpdateFlag() != null &&
                        interestBO.getCustomerBasicInfoWaiveInterestUpdateFlag()) {
                    customerBasicInfoSelfMapper.updateWaiveInterestFlag(OrgNumberUtils.getOrg(), interestBO.getCustomerId());
                }


            }

            if (interestBO.getEndInterestFlag() != null && interestBO.getEndInterestFlag() &&
                    interestBO.getRecorded().getTxnTransactionAmount().compareTo(BigDecimal.ZERO) > 0) {
                RecordedBO recorded = interestBO.getRecorded();
                txnRecordedService.txnRecorded(recorded);
                //更新交易账户

//                accountBalanceInfoSelfMapper.update4Recorded( Date.from( recorded.getLastInterestBillBate().atStartOfDay(ZoneId.systemDefault()).toInstant()),
                accountBalanceInfoSelfMapper.update4Recorded(recorded.getLastInterestBillBate(),
                        recorded.getTxnParentTransactionAccountId(),
                        interestBO.getBalanceAccount().getAccrueInterest().setScale(2, BigDecimal.ROUND_HALF_UP),
                        interestBO.getBalanceAccount().getActualInterestBillingDate().plusMonths(1));
            }

            /** poc */
            //计息历史
            logger.info("Calling organizationInfoService.findOrganizationInfo, accountId={}", interestBO.getBalanceAccount().getTransactionBalanceId());
            OrganizationInfoResDTO organizationInfo =
                    organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
            logger.info("organizationInfoService.findOrganizationInfo call completed, accountId={}", interestBO.getBalanceAccount().getTransactionBalanceId());


            String flag = organizationInfo.getInterestHistoryFlag();
            if ("1".equals(flag) && interestBO.getLogBOList() != null) {
                interestBO.getLogBOList().forEach(logBO ->
                        createInterestLogHistoryDtoForBatch(logBO.getAccountBalanceInfo(),
                                logBO.getAccountManagementInfo(),
                                logBO.getInterestProcessResult(),
                                logBO.getFlag())
                );
            }

        });
    }

//    private void createInterestLogHistory(AccountBalanceInfo accountBalanceInfo,
//                                          AccountManagementInfo accountManagementInfo,
//                                          InterestProcessResultBO interestProcessResult,
//                                          BatchInterestFlag flag) {
//        BeanCopier copier = BeanCopier.create(AccountBalanceInfo.class, AccountBalanceInfoDTO.class, false);
//        AccountBalanceInfoDTO accountBalanceInfoDTO = new AccountBalanceInfoDTO();
//        copier.copy(accountBalanceInfo, accountBalanceInfoDTO, null);
//
//        BeanCopier copier2 = BeanCopier.create(AccountManagementInfo.class, AccountManagementInfoDTO.class, false);
//        AccountManagementInfoDTO accountManagementInfoDTO = new AccountManagementInfoDTO();
//        copier2.copy(accountManagementInfo, accountManagementInfoDTO, null);
//        createInterestLogHistory(accountBalanceInfoDTO, accountManagementInfoDTO, interestProcessResult, flag);
//
//    }

    /**
     * 溢缴款利息累积
     *
     * @param accountBalanceInfo
     * @param txnBillingDate
     * @param organizationInfoResDTO
     * @return
     */
    @Override
    public InterestProcessResultBO accureOverpayInterest(AccountBalanceInfo accountBalanceInfo, LocalDate txnBillingDate, OrganizationInfoResDTO organizationInfoResDTO) {
        if (accountBalanceInfo.getInterestAccrueDate() != null && accountBalanceInfo.getInterestAccrueDate().compareTo(txnBillingDate.minusDays(1)) < 0
                && InterestAccrualMethodEnum.EVENT_ACCRUAL.getCode().equals(organizationInfoResDTO.getInterestAccrualMethod())) {
            //余额变动触发贷记利息累积
            InterestProcessResultBO interestProcessResultBO = new InterestProcessResultBO();
            //溢缴款累积基数
            //累积积数更新方式：aggregate_amount = aggregate_amount + （增加的余额 * 计息天数），同时更新上一利息累积日期为（入账日期-1天）
            BigDecimal oldAggregateAmount = accountBalanceInfo.getAggregateAmount();
            //计息天数
            long numberOfDays = Math.abs(accountBalanceInfo.getInterestAccrueDate().toEpochDay() - txnBillingDate.toEpochDay());
            BigDecimal aggregateAmount = oldAggregateAmount.add(accountBalanceInfo.getBalance().multiply(new BigDecimal(numberOfDays).setScale(6, BigDecimal.ROUND_HALF_UP)));
            interestProcessResultBO.setAccrueInterest(aggregateAmount);
            //上一利息累积日期
            interestProcessResultBO.setLastInterestAccrueDate(accountBalanceInfo.getInterestAccrueDate());
            //利息累积日期
            interestProcessResultBO.setInterestAccrueDate(txnBillingDate.minusDays(1));
            return interestProcessResultBO;
        }
        return null;
    }


    /* *//**
     * 还款无条件差额回算利息
     * @param accountBalanceInfo AccountBalanceInfoDTO
     * @param accountManagementInfoDTO AccountManagementInfoDTO
     * @param paramInterest InterestResDTO
     * @param recorded
     * @param balance
     * @return
     *//*
    private void unconditionDiffRepaymentBackInterest(AccountBalanceInfoDTO accountBalanceInfo,AccountManagementInfoDTO accountManagementInfoDTO,
                    InterestResDTO paramInterest,Recorded recorded,BigDecimal balance,InterestProcessResultBO interestProcessResultBO){
        //回算天数 = 开始日期（如果结过利息，取上一帐单日+1天，如果没有结过息，取起息日）~贷记调整入账日前1天
        LocalDate startAccDate;
        if(null != accountBalanceInfo.getLastCycleBillInterest() && accountBalanceInfo.getLastCycleBillInterest().compareTo(BigDecimal.ZERO) >0){
            startAccDate = accountManagementInfoDTO.getLastStatementDate().plusDays(1);
        }else {
            startAccDate = accountBalanceInfo.getInterestStartDate();
        }
        List<SegmentedInterestBO> dailyInterestList = calculateDailyInterstDuringDate(accountBalanceInfo,paramInterest,
                startAccDate, recorded.getTxnBillingDate());
        for (SegmentedInterestBO segmentedInterest : dailyInterestList) {
            LocalDate startDate = segmentedInterest.getStartDate();
            LocalDate endDate = segmentedInterest.getEndDate();
            InterestResDTO interestResDTO = segmentedInterest.getInterestResDTO();
            InterestCalcResultBO interestCalcResultBO = interestAccureCalcService.backInterest(startDate,endDate, balance,accountBalanceInfo.getCurrentEffectInterestRate());
            if (interestCalcResultBO != InterestCalcResultBO.ZERO) {
                InterestLogHistoryDTO interestLogHistoryDto = new InterestLogHistoryDTO();
                interestLogHistoryDto.setProssingIndicator(InterestProssingIndicator.C.getCode());
                interestLogHistoryDto.setCurrentInterestRate(interestResDTO.getBaseRate());
                recordInterestHistory(accountBalanceInfo, accountManagementInfoDTO, interestProcessResultBO, interestLogHistoryDto
                        , interestCalcResultBO);
            }
        }
    }*/

    /**
     * 获取计息方式
     *
     * @param paramOrganizationInfo OrganizationInfoResDTO
     * @param balanceAccount        AccountBalanceInfoDTO
     * @param managementAccount     AccountManagementInfoDTO
     * @param paramInterest         InterestResDTO
     * @param statementInfo         statementInfo
     * @return InterestCalculateOption
     */
    @Override
    public InterestCalculateOptionEnum getInterestAccMethod(OrganizationInfoResDTO paramOrganizationInfo,
                                                            AccountBalanceInfoDTO balanceAccount,
                                                            AccountManagementInfoDTO managementAccount,
                                                            InterestResDTO paramInterest, AccountStatementInfo statementInfo) {
        // 当前累计日期
        LocalDate accruedThruDay = paramOrganizationInfo.getAccruedThruDay();
        // 上一累计日
        LocalDate lastAccruedThruDay = paramOrganizationInfo.getLastAccruedThruDay();
        // 实际出账单日
        LocalDate actualStatementDate = balanceAccount.getActualStatementDate();
        // 基于账单处理参数表获取计息方式
        logger.info("Calling productInfoService.findProductInfo, organizationNumber={}, productNumber={}, currency={}", managementAccount.getOrganizationNumber(), managementAccount.getProductNumber(), managementAccount.getCurrency());  
        List<ProductInfoResDTO> paramProductInfoList = productInfoService.findProductInfo(managementAccount.getOrganizationNumber(),
                managementAccount.getProductNumber(), managementAccount.getCurrency());
        logger.info("productInfoService.findProductInfo call completed, paramProductInfoList={}", paramProductInfoList);
        String statementProcessingTableId = paramProductInfoList.get(0).getStatementProcessingTableId();
        StatementProcessResDTO paramStatementProcess = statementProcessService.findByOrgAndTableId(managementAccount.getOrganizationNumber(), statementProcessingTableId);
        String interestCalculateOption = paramStatementProcess.getInterestCalculateOption();
        // 计算当前交易账户的账单周期
        Integer billCycle = InterestUtils.getBillCycle(balanceAccount.getCreateDate(), managementAccount.getLastStatementDate(), managementAccount.getPreviousStatementDate());

        // 全额计息标志(默认全额计息)

        // 账单日不在上一累计日(不含) - 当前累计日(含)之间(即非账单日)，进行全额累计
        if (actualStatementDate == null
                || !(actualStatementDate.isAfter(lastAccruedThruDay)
                && (actualStatementDate.isEqual(accruedThruDay)
                || actualStatementDate.isBefore(accruedThruDay)))) {
            return InterestCalculateOptionEnum.ALL;
        } else {
            // 非按账期汇总 && 免息标记标记为免息 && 账单周期 = 1， 需进行判断是否进行差额计息
            if (!CreateMethodEnum.CYCLE.getCode().equals(balanceAccount.getCreateMethod())
                    && GraceOptionEnum.HAVE.getCode().equals(paramInterest.getGraceOption())
                    && billCycle == 1) {

                // 计息方式 = 无条件差额计息
                if (InterestCalculateOptionEnum.PART_ONE.getCode().equals(interestCalculateOption)) {
                    return InterestCalculateOptionEnum.PART_ONE;

                    // 计息方式 = 还清最低额差额计息 && 管理账户应还款总额 = 0(已还清)
                } else if (InterestCalculateOptionEnum.PART_TWO.getCode().equals(interestCalculateOption)) {
                    if (managementAccount.getTotalDueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        return InterestCalculateOptionEnum.PART_TWO;
                    }

                    // 计息方式 = 还清全额一定比例差额计息
                } else if (InterestCalculateOptionEnum.PART_THREE.getCode().equals(interestCalculateOption)) {
                    //客户级批量计息,用传过来的数据
                    if (null == statementInfo) {
                        statementInfo = accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate(managementAccount.getAccountManagementId(), managementAccount.getLastStatementDate());
                    }
                    Boolean partThree = partThree(managementAccount, paramStatementProcess, statementInfo);
                    if (partThree) {
                        return InterestCalculateOptionEnum.PART_THREE;
                    }
                }
            }
        }

        return InterestCalculateOptionEnum.ALL;
    }

    /**
     * 判断计息方式是全额计息还是差额计息,true:全额计息、false:差额计息
     *
     * @param balanceAccount    AccountBalanceInfoDTO
     * @param managementAccount AccountManagementInfoDTO
     * @param paramInterest     InterestResDTO
     * @return Boolean
     */
    @Override
    public Boolean getFullInterest(AccountBalanceInfoDTO balanceAccount,
                                   AccountManagementInfoDTO managementAccount,
                                   InterestResDTO paramInterest) {
        OrganizationInfoResDTO paramOrganizationInfo = getParamOrganizationInfo(balanceAccount);
        return getFullInterest(paramOrganizationInfo, balanceAccount, managementAccount, paramInterest, null);
    }

    /**
     * 判断计息方式是全额计息还是差额计息(客户级批量用),true:全额计息、false:差额计息
     *
     * @param paramOrganizationInfo OrganizationInfoResDTO
     * @param balanceAccount        AccountBalanceInfoDTO
     * @param managementAccount     AccountManagementInfoDTO
     * @param paramInterest         InterestResDTO
     * @param statementInfo         AccountStatementInfo
     * @return Boolean
     */
    @Override
    public Boolean getFullInterest(OrganizationInfoResDTO paramOrganizationInfo,
                                   AccountBalanceInfoDTO balanceAccount,
                                   AccountManagementInfoDTO managementAccount,
                                   InterestResDTO paramInterest, AccountStatementInfo statementInfo) {
        InterestCalculateOptionEnum interestAccMethod = getInterestAccMethod(paramOrganizationInfo, balanceAccount, managementAccount, paramInterest, statementInfo);
        return Objects.equals(interestAccMethod, InterestCalculateOptionEnum.ALL);
    }

    /**
     * 还清全额一定比例差额计息方式下是否需要等额计息
     *
     * @param managementAccount     AccountManagementInfoDTO
     * @param paramStatementProcess StatementProcessResDTO
     * @param statementInfo         AccountStatementInfo
     * @return Boolean
     */
    private Boolean partThree(AccountManagementInfoDTO managementAccount,
                              StatementProcessResDTO paramStatementProcess,
                              AccountStatementInfo statementInfo) {
        //还清全额一定比例差额计息
        BigDecimal closeBalance = statementInfo.getCloseBalance();
        return closeBalance != null && isFullPaymentPercentage(managementAccount, closeBalance, paramStatementProcess);
    }

    /**
     * 判断是否还清一定比例金额
     *
     * @param managementAccount     管理账户
     * @param closeBalance          账单期末余额
     * @param paramStatementProcess 还清全额百分比
     * @return Boolean
     */
    private Boolean isFullPaymentPercentage(AccountManagementInfoDTO managementAccount,
                                            BigDecimal closeBalance,
                                            StatementProcessResDTO paramStatementProcess) {
        // 宽限期内还款金额+ 宽限期内往期贷调总金额 >= 期末余额 * 还清全额百分比
        return managementAccount.getTotalGracePaymentAmount().
                add(managementAccount.getLastCycleCreditAdjAmount()).
                compareTo(closeBalance.multiply(paramStatementProcess.getFullPaymentPercentage())) >= 0;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
