package com.anytech.anytxn.transaction.service.gv;

import com.anytech.anytxn.business.dao.authorization.mapper.CardVelocityControlSellfMapper;
import com.anytech.anytxn.business.dao.authorization.model.CardVelocityControl;
import com.anytech.anytxn.business.dao.card.mapper.CardReissueInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardReissueInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateTopDownReferenceSelfMapper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.base.customer.enums.AddressTypeEnum;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAddressInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAddressInfo;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoMapper;
import com.anytech.anytxn.business.dao.limit.model.LimitCustCreditInfo;
import com.anytech.anytxn.limit.base.enums.LimitTypeEnum;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCountryCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCountryCodeSelfMapper;
import com.anytech.anytxn.transaction.base.domain.dto.gv.AbtFileDTO;
import com.anytech.anytxn.transaction.base.domain.dto.gv.AccountsFileDTO;
import com.anytech.anytxn.transaction.base.domain.dto.gv.AcctBalanceGvFileDTO;
import com.anytech.anytxn.transaction.base.domain.dto.gv.SupplementDTO;
import com.anytech.anytxn.transaction.base.domain.dto.gv.SupplementalFieldsDTO;
import com.anytech.anytxn.transaction.base.domain.dto.gv.TransactionFileDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.ZoneId.systemDefault;

@Service
@Slf4j
public class AccountsFileServiceImpl {
    private FastDateFormat FORMAT_DATE_YYYMMMDD = FastDateFormat.getInstance("yyyyMMdd");

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    private CardBasicInfoSelfMapper cardBasicInfoSelfMapper;

    private CorporateCustomerInfoSelfMapper corporateCustomerInfoSelfMapper;

    private CustomerAddressInfoSelfMapper customerAddressInfoSelfMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private CardVelocityControlSellfMapper cardVelocityControlSellfMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    AcctBalanceGvFileServiceImpl acctBalanceGvFileService;
    @Autowired
    private TransactionFileServiceImpl transactionFileService;
    @Autowired
    private ParmCountryCodeSelfMapper countryCodeSelfMapper;
    @Autowired
    private CorporateTopDownReferenceSelfMapper topDownReferenceSelfMapper;
    @Autowired
    private CardReissueInfoMapper reissueInfoMapper;
    @Autowired
    private LimitCustCreditInfoMapper limitCustCreditInfoMapper;
    public final static DateTimeFormatter formatterTm = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public AbtFileDTO buildAccountsFileInfo(CorporateCustomerInfo corporateCustomerInfo) {
        init(sqlSessionFactory);
        List<AccountsFileDTO> accountsFileDTOList = Lists.newArrayList();//账户dto 10、20、30、40
        List<AcctBalanceGvFileDTO> balanceGvFileDTOS = new ArrayList<>();//余额dto
        List<TransactionFileDTO> transactionFileDTOS = new ArrayList<>();//交易dto
        List<SupplementalFieldsDTO> supplementalFieldsDTOS = new ArrayList<>();//补充信息dto
        if (Objects.nonNull(corporateCustomerInfo)) {
            List<CorporateCustomerInfo> corporateCustomerInfos = new ArrayList<>();
            corporateCustomerInfos.add(corporateCustomerInfo);
            //********因线上downtop还没有迁移，且现在只有两层结构
            List<CorporateCustomerInfo> customerInfos = null;
            if(!"0".equals(corporateCustomerInfo.getHierarchyLevel())){
                customerInfos = corporateCustomerInfoSelfMapper.selectByParentCorCustId(corporateCustomerInfo.getCorporateCustomerId());
            }
//            List<String> corporateChildIds = topDownReferenceSelfMapper.selectByCorporateParentId(corporateCustomerInfo.getCorporateCustomerId());
//            List<CorporateCustomerInfo> customerInfos = null;
//              if(corporateChildIds != null && corporateChildIds.size() > 0){
//                  customerInfos = corporateCustomerInfoSelfMapper.selectByCorpCusIdList(corporateChildIds);
//              }
            if (customerInfos != null && customerInfos.size() > 0) {
                corporateCustomerInfos.addAll(customerInfos);
            }
            if (CollectionUtils.isNotEmpty(corporateCustomerInfos)) {
                for (CorporateCustomerInfo customerInfo : corporateCustomerInfos) {
                    //判断读出的公司信息中Global Vision='Y'
                    if (Objects.nonNull(customerInfo)
                            && (StringUtils.isNotBlank(customerInfo.getGlobalVision()) && customerInfo.getGlobalVision().equals("Y"))) {
                        //遍历顶层结构构建公司卡层级关系
                        buildResponseInfo(customerInfo, accountsFileDTOList, balanceGvFileDTOS, transactionFileDTOS, supplementalFieldsDTOS);
                    }
                }
            }
        }
        return new AbtFileDTO(accountsFileDTOList, balanceGvFileDTOS, transactionFileDTOS, supplementalFieldsDTOS);
    }


    private void buildResponseInfo(CorporateCustomerInfo corporateCustomerInfo, List<AccountsFileDTO> accountsFileDTOList, List<AcctBalanceGvFileDTO> balanceGvFileDTOS, List<TransactionFileDTO> transactionFileDTOS, List<SupplementalFieldsDTO> supplementalFieldsDTOS) {

        String hierarchyLevel = corporateCustomerInfo.getHierarchyLevel();
        //构建头信息
        AccountsFileDTO accountsFileDTO = getAccountsFileLevelHeaderInfo(corporateCustomerInfo, null);

        if (hierarchyLevel.equals("0")) {

            //公司客户信息的更新时间为机构的当前日期则构建30层级
            if (ifBuildCorpLevel(corporateCustomerInfo)) {

                buildAccountsFileInfoLevel30(corporateCustomerInfo, null, accountsFileDTO);
                accountsFileDTOList.add(accountsFileDTO);
            }

            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCorpCusId(corporateCustomerInfo.getOrganizationNumber(), corporateCustomerInfo.getCorporateCustomerId());
            for (AccountManagementInfo accountManagementInfo : accountManagementInfos) {
                List<CardAuthorizationInfo> cardAuthorizationInfos = new ArrayList<>();
                if("P".equals(accountManagementInfo.getLiability())){
                    cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByOrgNumAndCustId(corporateCustomerInfo.getOrganizationNumber(),accountManagementInfo.getCustomerId(), accountManagementInfo.getProductNumber());
                }else{
                    cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByOrgCorpCusIdAndProdId(corporateCustomerInfo.getOrganizationNumber(), corporateCustomerInfo.getCorporateCustomerId(),accountManagementInfo.getProductNumber());
                }

                cardAuthorizationInfos = cardAuthorizationInfos.stream()
                        .filter(cardAuthorizationInfo -> cardAuthorizationInfo.getProductNumber().equals(accountManagementInfo.getProductNumber()) && cardAuthorizationInfo.getRelationshipIndicator().equals("P"))
                        .sorted(Comparator.comparing(CardAuthorizationInfo::getCreateTime))
                        .collect(Collectors.toList());
               //排除非DCS卡=非（36开头+1111开头)
                cardAuthorizationInfos = cardAuthorizationInfos.stream().filter(t1->t1.getCardNumber().startsWith("36") ||t1.getCardNumber().startsWith("1111") )
                              .collect(Collectors.toList());
                String accountNumberFor20 = accountManagementInfo.getExternalReferenceNumber();
                if(StringUtils.isNotEmpty(accountNumberFor20)){
                    accountNumberFor20 = accountNumberFor20.length() > 13 ? accountNumberFor20.substring(0, 13) : accountNumberFor20;
                }
                if (cardAuthorizationInfos != null && cardAuthorizationInfos.size() > 0 && cardAuthorizationInfos.get(0).getCardNumber().startsWith("111")) {
                    String cardNumber = cardAuthorizationInfos.get(0).getCardNumber();
                    accountNumberFor20 = cardNumber.length() > 13 ? cardNumber.substring(0, 13) : cardNumber;
                }
                //管理账户的开户日期为机构的today则构建20层级
                if (ifBuildAccLevel(accountManagementInfo,accountNumberFor20,corporateCustomerInfo)) {

                    buildAccountsFileInfoLevel20(corporateCustomerInfo, accountManagementInfo, accountsFileDTOList, accountNumberFor20);
                }
                //构建余额文件，历史所有20数据并集
                if(StringUtils.isNotEmpty(accountNumberFor20)){
                    AcctBalanceGvFileDTO acctBalanceGvFileDTO = acctBalanceGvFileService.generateBalanceGvEntity(accountManagementInfo,accountNumberFor20,corporateCustomerInfo);
                    balanceGvFileDTOS.add(acctBalanceGvFileDTO);
                }

                for (CardAuthorizationInfo cardAuthorizationInfo : cardAuthorizationInfos) {
                    //构建层级10
                    if (ifBuildCard(cardAuthorizationInfo,corporateCustomerInfo)) {
                        buildAccountsFileInfoLevel10(corporateCustomerInfo, accountManagementInfo, accountsFileDTOList, cardAuthorizationInfo, accountNumberFor20);
                    }
                    //构建交易文件+补充信息文件
                    List<SupplementDTO> supplementDTOS = transactionFileService.buildTransactionFileInfo(cardAuthorizationInfo, accountNumberFor20);
                    List<TransactionFileDTO> transFileDTOs = supplementDTOS.stream().map(u -> u.getTransactionFileDTO()).collect(Collectors.toList());
                    transactionFileDTOS.addAll(transFileDTOs);
                    List<List<SupplementalFieldsDTO>> supplementalFieldsDTOS1 = supplementDTOS.stream().map(u -> u.getSupplementalFieldsDTOList()).collect(Collectors.toList());
                    for (List<SupplementalFieldsDTO> temp : supplementalFieldsDTOS1) {
                        supplementalFieldsDTOS.addAll(temp);
                    }
                }
            }
        } else if (hierarchyLevel.equals("9")) {

            //公司客户信息的更新时间为机构的当前日期则构建40层级
            if (ifBuildCorpLevel(corporateCustomerInfo)){
                buildAccountsFileInfoLevel40(corporateCustomerInfo, null, accountsFileDTO);
                accountsFileDTOList.add(accountsFileDTO);
            }
        }

    }
    private AccountsFileDTO getAccountsFileLevelHeaderInfo(CorporateCustomerInfo corporateCustomerInfo, AccountManagementInfo accountManagementInfo) {
        AccountsFileDTO accountsFileDTO = new AccountsFileDTO();
        accountsFileDTO.setParticipantCode("RZ");
        accountsFileDTO.setAccountNumber(""); //20 10 在各自方法中重置

        if (corporateCustomerInfo.getHierarchyLevel().equals("9") || corporateCustomerInfo.getHierarchyLevel().equals("0")) {
            accountsFileDTO.setAccountNumber(corporateCustomerInfo.getCorporateCustomerId());
        }

        boolean flag = false;

        LocalDate globalVersionEnromentDate = corporateCustomerInfo.getGlobalVisionEnrolmentDate();
        LocalDate localDate = corporateCustomerInfo.getCreateTime().toLocalDate();

        /*if (globalVersionEnromentDate != null && globalVersionEnromentDate.compareTo(getOrganizationDate()) == 0) {
            flag = true;
        }*/
        if (localDate != null && localDate.compareTo(getOrganizationDate()) == 0) {
            flag = true;
        }
        if (flag) {
            accountsFileDTO.setRecordType("I");
        } else {
            accountsFileDTO.setRecordType("U");
        }
        return accountsFileDTO;
    }

    private void buildAccountsFileInfoLevel10(CorporateCustomerInfo corporateCustomerInfo, AccountManagementInfo accountManagementInfo, List<AccountsFileDTO> accountsFileDTOList, CardAuthorizationInfo cardAuthorizationInfo, String accountNumberFor20) {

        AccountsFileDTO accountsFileDTO = getAccountsFileLevelHeaderInfo(corporateCustomerInfo, accountManagementInfo);
        if (cardAuthorizationInfo.getOpenDate() != null && cardAuthorizationInfo.getOpenDate().compareTo(getOrganizationDate()) == 0) {
            accountsFileDTO.setRecordType("I");
        } else {
           /* if (ObjectUtils.isNotEmpty(corporateCustomerInfo.getGlobalVisionEnrolmentDate()) && corporateCustomerInfo.getGlobalVisionEnrolmentDate().compareTo(getOrganizationDate()) == 0){
                accountsFileDTO.setRecordType("I");
            }else {
                accountsFileDTO.setRecordType("U");
            }*/
            accountsFileDTO.setRecordType("U");
        }

        getBasicInfo(corporateCustomerInfo, accountManagementInfo, accountsFileDTO, "10",cardAuthorizationInfo);
        accountsFileDTO.setAccountLevel("10");

        if (accountsFileDTO.getAccountLevel().equals("10")) {
            //层级为10赋值管理账户
            accountsFileDTO.setLinkAccountNumber(accountManagementInfo.getAccountManagementId());
            accountsFileDTO.setBalanceAccountNumber(accountManagementInfo.getAccountManagementId());
            CardBasicInfo cardBasicInfo = cardBasicInfoSelfMapper.selectByOrgAndCardNumber(accountManagementInfo.getOrganizationNumber(), cardAuthorizationInfo.getCardNumber());
            accountsFileDTO.setIndicativeData1(cardBasicInfo.getDepartmentCode());
            accountsFileDTO.setIndicativeData2(cardBasicInfo.getStaffId());
            accountsFileDTO.setIndicativeData3(cardBasicInfo.getCostCentre());
        }

        getBillingInfo(accountsFileDTO, accountManagementInfo,cardAuthorizationInfo);

        getAdditionalInformation(accountsFileDTO, accountManagementInfo);

        //重置 accountNumber
        accountsFileDTO.setAccountNumber(cardAuthorizationInfo.getCardNumber());
        //重置linkAccountNumber
        accountsFileDTO.setLinkAccountNumber(accountNumberFor20);
        //重置balanceAccountNumber
        accountsFileDTO.setBalanceAccountNumber(accountNumberFor20);

        accountsFileDTOList.add(accountsFileDTO);
    }


    private void buildAccountsFileInfoLevel20(CorporateCustomerInfo corporateCustomerInfo, AccountManagementInfo accountManagementInfo, List<AccountsFileDTO> accountsFileDTOList, String accountNumberFor20) {
        AccountsFileDTO accountsFileDTO = getAccountsFileLevelHeaderInfo(corporateCustomerInfo, accountManagementInfo);
        //重置recordType
        if (accountManagementInfo.getOpenDate() != null && accountManagementInfo.getOpenDate().compareTo(getOrganizationDate()) == 0) {
            accountsFileDTO.setRecordType("I");
        } else {
           /* if (ObjectUtils.isNotEmpty(corporateCustomerInfo.getGlobalVisionEnrolmentDate()) && corporateCustomerInfo.getGlobalVisionEnrolmentDate().compareTo(getOrganizationDate()) == 0){
                accountsFileDTO.setRecordType("I");
            }else {
                accountsFileDTO.setRecordType("U");
            }*/
            accountsFileDTO.setRecordType("U");
        }
        getBasicInfo(corporateCustomerInfo, accountManagementInfo, accountsFileDTO, "20",null);

        getBillingInfo(accountsFileDTO, accountManagementInfo,null);

        getAdditionalInformation(accountsFileDTO, accountManagementInfo);
        //重置accountNumber
        accountsFileDTO.setAccountNumber(accountNumberFor20);
        accountsFileDTO.setBalanceAccountNumber(accountNumberFor20);
        accountsFileDTOList.add(accountsFileDTO);
    }

    private void buildAccountsFileInfoLevel30(CorporateCustomerInfo corporateCustomerInfo, AccountManagementInfo accountManagementInfo, AccountsFileDTO accountsFileDTO) {

        getBasicInfo(corporateCustomerInfo, accountManagementInfo, accountsFileDTO, "30",null);

        getBillingInfo(accountsFileDTO, accountManagementInfo,null);

        getAdditionalInformation(accountsFileDTO, accountManagementInfo);
    }


    private void buildAccountsFileInfoLevel40(CorporateCustomerInfo corporateCustomerInfo, AccountManagementInfo accountManagementInfo, AccountsFileDTO accountsFileDTO) {

        getBasicInfo(corporateCustomerInfo, accountManagementInfo, accountsFileDTO, "40",null);

        getBillingInfo(accountsFileDTO, accountManagementInfo,null);

        getAdditionalInformation(accountsFileDTO, accountManagementInfo);
        /**
         *  2023-07-28这一天的创建的公司accountNumber赋值reg id 匹配30层的公司信息)
         *  大于2023-07-01这一天创建的公司accountNumber赋值corp cus id  匹配30层的公司信息)
         *  小于2023-07-01这一天创建的公司accountNumber赋值reg id   匹配30层的公司信息)
          */
        setAccountNUmberLevel(corporateCustomerInfo,accountsFileDTO,"40");
        // level40送credit_limit_amount sc99
        LimitCustCreditInfo limitCustCreditInfo = limitCustCreditInfoMapper.selectByCustomerIdAndLimitTypeCode(corporateCustomerInfo.getCorporateCustomerId(), LimitTypeEnum.SC99.getCode(), OrgNumberUtils.getOrg());
        accountsFileDTO.setCreditLimitAmount(limitCustCreditInfo==null?BigDecimal.ZERO:limitCustCreditInfo.getFixLimitAmount());
    }

    private void getBillingInfo(AccountsFileDTO accountsFileDTO, AccountManagementInfo accountManagementInfo,CardAuthorizationInfo cardAuthorizationInfo) {
//        CardAuthorizationInfo cardAuthorizationInfo = null;
        accountsFileDTO.setLastBillingDate(Arrays.asList("20", "10").contains(accountsFileDTO.getAccountLevel()) ? dateFormater(accountManagementInfo.getLastStatementDate()) : "");
        accountsFileDTO.setAccountStatus("");
        accountsFileDTO.setExpirationDate("");
        accountsFileDTO.setDateCardIssued(LocalDate.of(1, 1, 1));
        accountsFileDTO.setRenewalDate("");
        accountsFileDTO.setDPASEMVChipEnabled("F");

        //挂卡层级赋值
        if (accountsFileDTO.getAccountLevel().equals("10")) {
//            List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId());
//            cardAuthorizationInfo = cardAuthorizationInfos.stream().sorted(Comparator.comparing(CardAuthorizationInfo::getCreateTime)).findFirst().orElse(null);
            if (cardAuthorizationInfo != null) {
                String cardExpirationDate = "20".concat(cardAuthorizationInfo.getExpireDate()).concat("01");
                String formateDate = cardExpirationDate.substring(0, 4).concat("-").concat(cardExpirationDate.substring(4, 6)).concat("-").concat(cardExpirationDate.substring(6));
                String endDayOfMonth = FORMAT_DATE_YYYMMMDD.format(getEndDayOfMonth(formateDate));
//                String endDayOfMonth = FORMAT_DATE_YYYMMMDD.format(getEndDayOfMonth(formateDate));

                accountsFileDTO.setExpirationDate(endDayOfMonth);//取当月最后一天
                List<CardReissueInfo> reissueInfos = reissueInfoMapper.selectByCardNumber(cardAuthorizationInfo.getCardNumber(),cardAuthorizationInfo.getOrganizationNumber());
                CardReissueInfo reissueInfo = reissueInfos.stream().sorted(Comparator.comparing(CardReissueInfo::getCreateTime)).findFirst().orElse(null);
                String renewalDate = "";
                if(reissueInfo != null){
                    DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
                    if (reissueInfo.getReissueDate()!=null) {
                        renewalDate = yyyyMMdd.format(reissueInfo.getReissueDate());
                    }
                }
                accountsFileDTO.setRenewalDate(renewalDate);
                accountsFileDTO.setDateCardIssued(cardAuthorizationInfo.getOpenDate());
                accountsFileDTO.setAccountStatus(this.getBlockCodeStatus(cardAuthorizationInfo).concat(" ").concat(dateFormater(cardAuthorizationInfo.getBlockCodeDate())));
            }
            accountsFileDTO.setDPASEMVChipEnabled("T");
        } else if (accountsFileDTO.getAccountLevel().equals("20")) {
            List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId());
            cardAuthorizationInfo = cardAuthorizationInfos.stream().sorted(Comparator.comparing(CardAuthorizationInfo::getCreateTime)).findFirst().orElse(null);
            if (cardAuthorizationInfo != null) {
                String cardExpirationDate = "20".concat(cardAuthorizationInfo.getExpireDate()).concat("01");
                String formateDate = cardExpirationDate.substring(0, 4).concat("-").concat(cardExpirationDate.substring(4, 6)).concat("-").concat(cardExpirationDate.substring(6));
                String endDayOfMonth = FORMAT_DATE_YYYMMMDD.format(getEndDayOfMonth(formateDate));
                accountsFileDTO.setExpirationDate(endDayOfMonth);
            }
            accountsFileDTO.setDateCardIssued(accountManagementInfo.getOpenDate());//取当月最后一天
        }
        accountsFileDTO.setFeeBilledDate("");

        accountsFileDTO.setLastAddressNameChangeDate("");

        accountsFileDTO.setDistributionFlag("T");
        accountsFileDTO.setPhotocardFlag("F");
        accountsFileDTO.setFlightInsuranceFlag("F");
        accountsFileDTO.setATMFlag("T");
        accountsFileDTO.setRewardsFlag("T");
        accountsFileDTO.setPhoneFlag("F");

        accountsFileDTO.setProductCode2("F");
        accountsFileDTO.setProductCode3("F");
        accountsFileDTO.setProductCode4("F");
        accountsFileDTO.setProductCode5("F");


        accountsFileDTO.setAirlineCentralBillAccountNumber("");
        accountsFileDTO.setCarRentalCentralBillAcctNumber("");
        accountsFileDTO.setHotelCentralBillAccountNumber("");
        accountsFileDTO.setFeeCentralBillAccountNumber("");
        accountsFileDTO.setRestaurantCentralBillAcctNumber("");
        //默认值给？
        accountsFileDTO.setRenewalFee(BigDecimal.ZERO);

        accountsFileDTO.setDCICountryFlag("F");
        accountsFileDTO.setSourceCode("");

        accountsFileDTO.setFiller("");
        BigDecimal creditLimitAmount = BigDecimal.ZERO;
        LocalDate creditLimitDate = LocalDate.of(1, 1, 1);
        // DOTO 判断是公司清偿还是个人清偿 公司清偿需要根据卡号查询卡片流量表
        if (cardAuthorizationInfo != null &&
                StringUtils.isNotEmpty(cardAuthorizationInfo.getLiability()) && cardAuthorizationInfo.getLiability().equals("C")) {
            CardVelocityControl cardVelocityControl = cardVelocityControlSellfMapper.selectByCardNumAndVeleCode(cardAuthorizationInfo.getCardNumber(), "");
            if (Objects.nonNull(cardVelocityControl)) {
                creditLimitAmount = cardVelocityControl.getOverrideAmount();
                creditLimitDate = cardVelocityControl.getEffectiveDate();
            }

        }
        accountsFileDTO.setCreditLimitAmount(creditLimitAmount);
        accountsFileDTO.setCreditLimitDate(creditLimitDate);
        accountsFileDTO.setCustomerIDNumber("");
    }

    // 获取月第一天
    public static Date getStartDayOfMonth(String date) {
        LocalDate now = LocalDate.parse(date);
        return getStartDayOfMonth(now);
    }

    public static Date getStartDayOfMonth(LocalDate date) {
        LocalDate now = date.with(TemporalAdjusters.firstDayOfMonth());
        ZonedDateTime zonedDateTime = now.atStartOfDay(systemDefault());
        Instant instant1 = zonedDateTime.toInstant();
        Date from = Date.from(instant1);
        return from;
    }

    // 获取月最后一天
    public static Date getEndDayOfMonth(String date) {
        LocalDate localDate = LocalDate.parse(date);
        return getEndDayOfMonth1(localDate);
    }

    public static Date getEndDayOfMonth1(LocalDate date) {
        LocalDate now = date.with(TemporalAdjusters.lastDayOfMonth());

        Date.from(now.atStartOfDay(systemDefault()).plusDays(1L).minusNanos(1L).toInstant());
        ZonedDateTime zonedDateTime = now.atStartOfDay(systemDefault());
        Instant instant1 = zonedDateTime.toInstant();
        Date from = Date.from(instant1);
        return from;
    }

    /**
     * 1. 卡片封锁码为L1或者L2,赋值为7
     * 2. 卡片封锁码为S1，赋值为5
     * 3. 卡片封锁码为VA，赋值为Q
     * 4. 卡片封锁码为DA，赋值为P
     * 5. 卡片封锁码为O1，赋值为T
     * 6. 卡片封锁码为其他，赋值为空
     * @return
     */
    public String getBlockCodeStatus(CardAuthorizationInfo cardAuthorizationInfo){
        String blockCode = cardAuthorizationInfo.getBlockCode();
        if(StringUtils.isEmpty(blockCode)){
            return " ";
        }else if("L1".equals(blockCode) || "L2".equals(blockCode)){
            return "7";
        }else if("S1".equals(blockCode)){
            return "5";
        }else if("VA".equals(blockCode)){
            return "Q";
        }else if("DA".equals(blockCode)){
            return "P";
        }else if("o1".equals(blockCode)){
            return "T";
        }
        return " ";
    }

    private void getBasicInfo(CorporateCustomerInfo corporateCustomerInfo, AccountManagementInfo accountManagementInfo, AccountsFileDTO accountsFileDTO, String accountLevel,CardAuthorizationInfo cardAuthorizationInfo) {
        if ("40".equals(accountLevel)) {
            accountsFileDTO.setLinkAccountNumber("");
        } else if ("30".equals(accountLevel)) {
            CorporateCustomerInfo customerInfo = corporateCustomerInfoSelfMapper.selectByOrgNumAndCusId(corporateCustomerInfo.getOrganizationNumber(), corporateCustomerInfo.getParentCorporateCustomerId());
            accountsFileDTO.setLinkAccountNumber("");
            if (customerInfo != null) {

                this.setAccountNUmberLevel(customerInfo,accountsFileDTO,"30");

            }
        } else if ("20".equals(accountLevel)) {

            this.setAccountNUmberLevel(corporateCustomerInfo,accountsFileDTO,"20");

        } else if ("10".equals(accountLevel)) {
            accountsFileDTO.setLinkAccountNumber(accountManagementInfo.getAccountManagementId());
        } else {
            accountsFileDTO.setLinkAccountNumber("");
        }

        //挂卡层级赋值为管理账户
        if ("10".equals(accountLevel)) {
            accountsFileDTO.setBalanceAccountNumber(accountManagementInfo.getAccountManagementId());
        } else {
            accountsFileDTO.setBalanceAccountNumber("");
        }

        //设置公司名称 title 信息
        CardBasicInfo cardBasicInfo = null;
        if (accountLevel.equals("10")) {
            String cardNumber = cardAuthorizationInfo.getCardNumber();
            cardBasicInfo = cardBasicInfoSelfMapper.selectByOrgAndCardNumber(OrgNumberUtils.getOrg(), cardNumber);
        }

        if (Objects.nonNull(cardBasicInfo) && StringUtils.isNotEmpty(cardBasicInfo.getEmbossingName1())) {
            accountsFileDTO.setAccountName(cardBasicInfo.getEmbossingName1());
            accountsFileDTO.setLastName(cardBasicInfo.getEmbossingName1());
            accountsFileDTO.setFirstName(cardBasicInfo.getEmbossingName1());
        }
        else {
            //没有挂卡的情况 TODO 名称是否需要截取
            accountsFileDTO.setAccountName(corporateCustomerInfo.getName() == null ? "" : corporateCustomerInfo.getName().length() > 35 ? corporateCustomerInfo.getName().substring(0, 35) : corporateCustomerInfo.getName());
            accountsFileDTO.setLastName(corporateCustomerInfo.getName() == null ? "" : corporateCustomerInfo.getName().length() > 26 ? corporateCustomerInfo.getName().substring(0, 26) : corporateCustomerInfo.getName());
            accountsFileDTO.setFirstName("");
        }



        accountsFileDTO.setTitle("");

        accountsFileDTO.setAccountProduct(Arrays.asList("40", "30").contains(accountLevel) ? "3" : "1");
        String billingCycle = "001";
        if ("40".equals(accountLevel)) {
            billingCycle = "";
        }
        accountsFileDTO.setBillingCycle(billingCycle);
        accountsFileDTO.setAccountLevel(accountLevel);

        accountsFileDTO.setBusinessPhoneNumber("");
        accountsFileDTO.setHomePhoneNumber("");

        if (Arrays.asList("10", "20").contains(accountLevel)) {

            CustomerAddressInfo customerAddressInfo = null;

            if (Objects.nonNull(accountManagementInfo)) {
                String statementAddressType = accountManagementInfo.getStatementAddressType();
                customerAddressInfo = customerAddressInfoSelfMapper.selectByCustomerIdAndType(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(), statementAddressType);
            }

            accountsFileDTO.setAddress1(customerAddressInfo == null ? "" : getSubStr(customerAddressInfo.getAddress(), 0, 35));
            accountsFileDTO.setAddress2(customerAddressInfo == null ? "" : getSubStr(customerAddressInfo.getAddress2(), 0, 35));
            accountsFileDTO.setAddress3(customerAddressInfo == null ? "" : getSubStr(customerAddressInfo.getAddress3(), 0, 35));
            accountsFileDTO.setAddress4(customerAddressInfo == null ? "" : getSubStr(customerAddressInfo.getAddress4(), 0, 35));

            accountsFileDTO.setStateProvince(customerAddressInfo == null ? "" : customerAddressInfo.getProvince());
            accountsFileDTO.setPostalCode(customerAddressInfo == null ? "" : customerAddressInfo.getZipcode());

            accountsFileDTO.setContactName("");
            accountsFileDTO.setContactTitle("");
            accountsFileDTO.setContactPhone("");

            accountsFileDTO.setDateAccountOpened(accountManagementInfo == null ? LocalDate.of(1, 1, 1) : accountManagementInfo.getOpenDate());
            if ("20".equals(accountLevel)) {
                accountsFileDTO.setDateRecordUpdated(dateFormater(accountManagementInfo.getUpdateTime().toLocalDate()));
            } else {
                accountsFileDTO.setDateRecordUpdated(cardBasicInfo != null ? dateFormater(cardBasicInfo.getUpdateTime().toLocalDate()) : "");
            }
        } else {
            CustomerAddressInfo customerAddressInfo = customerAddressInfoSelfMapper.selectByCustomerIdAndType(OrgNumberUtils.getOrg(), corporateCustomerInfo.getCorporateCustomerId(), AddressTypeEnum.REGISTRATION_ADDRESS.getCode());
            if (customerAddressInfo != null){
                accountsFileDTO.setAddress1(getSubStr(customerAddressInfo.getAddress(), 0, 35));
                accountsFileDTO.setAddress2(getSubStr(customerAddressInfo.getAddress2(), 0, 35));
                accountsFileDTO.setAddress3(getSubStr(customerAddressInfo.getAddress3(), 0, 35));
                accountsFileDTO.setAddress4(getSubStr(customerAddressInfo.getAddress4(), 0, 35));
                accountsFileDTO.setStateProvince(customerAddressInfo.getProvince());
                accountsFileDTO.setPostalCode(customerAddressInfo.getZipcode());
            }

            accountsFileDTO.setContactName(corporateCustomerInfo.getContactName());
            accountsFileDTO.setContactTitle(corporateCustomerInfo.getContactOccupation());
            accountsFileDTO.setContactPhone(corporateCustomerInfo.getContactMobile());

            accountsFileDTO.setDateAccountOpened(corporateCustomerInfo.getOpenDate());
            accountsFileDTO.setDateRecordUpdated(dateFormater(corporateCustomerInfo.getUpdateTime().toLocalDate()));
        }
//        accountsFileDTO.setCountryCode(corporateCustomerInfo.getCountry());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        accountsFileDTO.setCountryCode(organizationInfo.getOrganizationCurrency());
        ParmCountryCode parmCountryCode = countryCodeSelfMapper.selectAllCountryCodeByCountryCode(accountsFileDTO.getCountryCode());
        accountsFileDTO.setCityTown(parmCountryCode == null ? "" : getSubStr(parmCountryCode.getDescription(), 0, 25));
        String indicativeData1 = "";
        String indicativeData2 = "";
        String indicativeData3 = "";
        if ("10".equals(accountLevel) && cardBasicInfo != null) {
            indicativeData1 = cardBasicInfo.getDepartmentCode();
            indicativeData2 = cardBasicInfo.getStaffId();
            indicativeData3 = cardBasicInfo.getCostCentre();
        }
        accountsFileDTO.setIndicativeData1(getSubStr(indicativeData1,0,10));
        accountsFileDTO.setIndicativeData2(getSubStr(indicativeData2,0,10));
        accountsFileDTO.setIndicativeData3(getSubStr(indicativeData3,0,10));
        accountsFileDTO.setIndicativeData4("");
        accountsFileDTO.setIndicativeData5("");
        accountsFileDTO.setIndicativeData6("");

    }

    public String getSubStr(String str, int beginIndex, int endIndex) {
        if (StringUtils.isEmpty(str)) {
            return "";
        } else {
            if (str.length() > beginIndex) {
                if (str.length() < endIndex) {
                    return str.substring(beginIndex);
                } else {
                    return str.substring(beginIndex, endIndex);
                }
            } else {
                return "";
            }
        }
    }

    private void getAdditionalInformation(AccountsFileDTO accountsFileDTO, AccountManagementInfo accountManagementInfo) {
        accountsFileDTO.setPreviousStmtPointTotal("");
        accountsFileDTO.setPointsEarned("");
        accountsFileDTO.setBonusPointsEarned("");
        accountsFileDTO.setBonusPointsEarned2("");
        accountsFileDTO.setTotalPointsAdjusted("");
        accountsFileDTO.setExpiredPoints("");
        accountsFileDTO.setPointsPurchased("");
        accountsFileDTO.setPointsRedeemed("");
        accountsFileDTO.setPointsForfeited("");
        accountsFileDTO.setReinstated("");
        accountsFileDTO.setTotalPoints("");
        accountsFileDTO.setTotalPointsAvailable("");
        accountsFileDTO.setLifetimePointsEarned("");
        accountsFileDTO.setCorporateID("*********");
        accountsFileDTO.setCDTHotelFolioIndicator("F");
    }

    private String dateFormater(LocalDate localDate) {
        if (localDate != null) {
            DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
            return yyyyMMdd.format(localDate);
        }
        return "";
    }

    private void init(SqlSessionFactory sqlSessionFactory) {
        SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH);
        cardAuthorizationInfoSelfMapper = session.getMapper(CardAuthorizationInfoSelfMapper.class);
        cardBasicInfoSelfMapper = session.getMapper(CardBasicInfoSelfMapper.class);
        corporateCustomerInfoSelfMapper = session.getMapper(CorporateCustomerInfoSelfMapper.class);
        customerAddressInfoSelfMapper = session.getMapper(CustomerAddressInfoSelfMapper.class);
    }

    private LocalDate getOrganizationDate() {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        return organizationInfo.getToday();
    }
    private void setAccountNUmberLevel(CorporateCustomerInfo corporateCustomerInfo, AccountsFileDTO accountsFileDTO,String level){
        LocalDateTime lastModifyDateTime = corporateCustomerInfo.getCreateTime();
        if (corporateCustomerInfo.getGlobalVisionEnrolmentDate()!=null &&
                corporateCustomerInfo.getGlobalVisionEnrolmentDate().compareTo(corporateCustomerInfo.getCreateTime().toLocalDate())>0){
            lastModifyDateTime= corporateCustomerInfo.getGlobalVisionEnrolmentDate().atStartOfDay();
        }
        if (lastModifyDateTime.compareTo(LocalDateTime.parse("2023-07-28 00:00:00", formatterTm)) >= 0
                && lastModifyDateTime.compareTo(LocalDateTime.parse("2023-07-28 23:59:59", formatterTm)) <= 0) {
            if (level.equals("40")){
                accountsFileDTO.setAccountNumber(corporateCustomerInfo.getCorporateRegistrationId());
            }else if (Arrays.asList("30","20").contains(level)){
                accountsFileDTO.setLinkAccountNumber(corporateCustomerInfo.getRegistrationId());
            }
            else {}
        } else {
            if (lastModifyDateTime.compareTo(LocalDateTime.parse("2023-07-01 00:00:00", formatterTm)) >= 0) {
                if (level.equals("40")){
                    accountsFileDTO.setAccountNumber(corporateCustomerInfo.getCorporateCustomerId());
                }
                if (Arrays.asList("30","20").contains(level)){
                    accountsFileDTO.setLinkAccountNumber(corporateCustomerInfo.getCorporateCustomerId());
                }
            } else {
                if (level.equals("40")){
                    accountsFileDTO.setAccountNumber(corporateCustomerInfo.getRegistrationId());
                }else if(Arrays.asList("30","20").contains(level)){
                    accountsFileDTO.setLinkAccountNumber(corporateCustomerInfo.getRegistrationId());
                }
                else {}
            }
        }
    }
    private boolean ifBuildCard(CardAuthorizationInfo cardAuthorizationInfo,CorporateCustomerInfo corporateCustomerInfo) {
        boolean flag = false;
        if (cardAuthorizationInfo.getOpenDate().compareTo(getOrganizationDate()) == 0 ||
                cardAuthorizationInfo.getUpdateTime().toLocalDate().compareTo(getOrganizationDate()) == 0){
            flag= true;
        }
        if (corporateCustomerInfo.getGlobalVisionEnrolmentDate() != null && corporateCustomerInfo.getGlobalVisionEnrolmentDate().compareTo(getOrganizationDate())==0){
            flag=true;
        }
        return flag;
    }

    private boolean ifBuildAccLevel(AccountManagementInfo accountManagementInfo,String accountNumberFor20,CorporateCustomerInfo corporateCustomerInfo) {
        boolean flag = false;
        if (StringUtils.isNotEmpty(accountNumberFor20) && (accountManagementInfo.getOpenDate().compareTo(getOrganizationDate()) == 0
                || accountManagementInfo.getUpdateTime().toLocalDate().compareTo(getOrganizationDate()) == 0)){
            flag= true;
        }
        if (corporateCustomerInfo.getGlobalVisionEnrolmentDate() != null && corporateCustomerInfo.getGlobalVisionEnrolmentDate().compareTo(getOrganizationDate())==0){
            flag=true;
        }
        return flag;
    }

    private boolean ifBuildCorpLevel(CorporateCustomerInfo corporateCustomerInfo) {
        boolean flag = false;
        if(corporateCustomerInfo.getUpdateTime() != null && corporateCustomerInfo.getUpdateTime().toLocalDate().compareTo(getOrganizationDate()) == 0) {
            flag=true;
        }
        if (corporateCustomerInfo.getGlobalVisionEnrolmentDate() != null && corporateCustomerInfo.getGlobalVisionEnrolmentDate().compareTo(getOrganizationDate())==0){
            flag=true;
        }
        return flag;
    }
}