package com.anytech.anytxn.transaction.service;

import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTranAccountRelationDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoMapper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.enums.LiabilityEnum;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.authorization.mapper.OutstandingTransactionSelfMapper;
import com.anytech.anytxn.business.dao.authorization.model.OutstandingTransaction;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.base.transaction.domain.dto.DesignatedRepaymentReqBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTransactionBatchDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTransactionDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTransactionResDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.PostedTransactionTokenResDTO;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.limit.base.enums.VaTaTypeEnum;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysDictSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysDict;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.transaction.base.constants.Constants;
import com.anytech.anytxn.transaction.base.enums.PostMethodEnum;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.enums.AuthMatchIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.ReleaseAuthAmountEnum;
import com.anytech.anytxn.transaction.base.enums.RepostFromSuspendEnum;
import com.anytech.anytxn.transaction.base.enums.ReverseFeeIndicatorEnum;
import com.anytech.anytxn.transaction.base.domain.dto.partner.PartnerNotPostTransactionRespDTO;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.base.service.IPostedTransactionService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import com.anytech.anytxn.transaction.mapper.PostedTranAccountRelationInfoSelfMapper;
import com.anytech.anytxn.transaction.base.domain.model.PostedTranAccountRelationInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 已入账交易Service
 *
 * <AUTHOR>
 * @date 2019/1/9
 */
@Service
public class PostedTransactionServiceImpl implements IPostedTransactionService {


    @Autowired
    private PostedTransactionMapper postedTransactionMapper;
    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;
    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private AccountBalanceInfoMapper accountBalanceInfoMapper;
    @Autowired
    private PostedTranAccountRelationInfoSelfMapper postedTranAccountRelationInfoSelfMapper;

    @Autowired
    private InstallOrderMapper installOrderMapper;

    @Autowired
    private ParmTransactionCodeSelfMapper transactionCodeSelfMapper;

    @Autowired
    private OutstandingTransactionSelfMapper outstandingTransactionSelfMapper;

    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @Resource
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private ParmSysDictSelfMapper sysDictSelfMapper;

    private Map<String, String> authTransactionCodeDesc;

    @Resource
    private AccountStatementInfoSelfMapper statementInfoSelfMapper;

    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;

    @Autowired
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Autowired
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;

    @Autowired
    private SequenceIdGen sequenceIdGen;


    @PostConstruct
    private void getAuthTransactionCodeMap(){
        List<ParmSysDict> authTransactionType = sysDictSelfMapper.selectListByTypeId("AUTH_TRANSACTION_TYPE");
        authTransactionCodeDesc = authTransactionType.stream().collect(Collectors.toMap(ParmSysDict::getCodeId, ParmSysDict::getCodeDesc));
    }

    // 统一日志变量为logger
    private static final Logger logger = LoggerFactory.getLogger(PostedTransactionServiceImpl.class);

    @Override
    public PageResultDTO<PostedTransactionResDTO> findListPostTrans(Integer page,
                                                                    Integer rows,
                                                                    String accountStatementId) {
        logger.debug("Query posted transactions by account statement id: {}", accountStatementId);
        List<PostedTransactionResDTO> postedTransactionResList = new ArrayList<>();
        String organizationNumber = OrgNumberUtils.getOrg();
        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByAccStaId(
                accountStatementId, organizationNumber);
        Map<String, ParmTransactionCode> transactionCodeMap = getTransactionCodeMap(organizationNumber);
        for (PostedTransaction postedTransaction : postedTransactions) {
            PostedTransactionResDTO postedTransactionRes = new PostedTransactionResDTO();
            BeanUtils.copyProperties(postedTransaction, postedTransactionRes);
            TransactionCodeResDTO paramTransactionCode = BeanMapping.copy(transactionCodeMap.get(postedTransaction.getPostingTransactionCode()), TransactionCodeResDTO.class);
            if (paramTransactionCode == null) {
                logger.error(
                        "Failed to get transaction code parameters: organization number: {}, transaction code: {}",
                        postedTransaction.getOrganizationNumber(),
                        postedTransaction.getPostingTransactionCode());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
            }
            postedTransactionRes.setTransactionAttribute(paramTransactionCode.getTransactionAttribute());
            postedTransactionResList.add(postedTransactionRes);
        }
        for (PostedTransactionResDTO res : postedTransactionResList) {
            res.setRepayIden("1");
            res.setPostingTransactionCodeDesc(transactionCodeMap.get(res.getPostingTransactionCode()).getDescription());
            res.setPostIndicator("1");
        }

        List<PostedTransactionResDTO> trans = new ArrayList<>();

        List<PostedTransactionResDTO> listParentNode = new ArrayList<>();
        List<PostedTransactionResDTO> listNotParentNode = new ArrayList<>();

        List<PostedTransactionResDTO> listParentNode1 = new ArrayList<>();
        List<PostedTransactionResDTO> listNotParentNode1 = new ArrayList<>();

        List<PostedTransactionResDTO> transactionResDTOS = new ArrayList<>();
        for (PostedTransactionResDTO transactionResDTO : postedTransactionResList) {
            if (null != transactionResDTO) {
                String postingTransactionCode = transactionResDTO.getPostingTransactionCode();
                if (TransactionConstants.TRANSACTION_CODES.contains(postingTransactionCode)) {
                    transactionResDTOS.add(transactionResDTO);
                }
                if (TransactionConstants.PN_TRANSACTION_CODES.contains(postingTransactionCode)) {
                    AccountBalanceInfo balanceInfo = accountBalanceInfoMapper.selectByPrimaryKey(transactionResDTO.getOriginalPostedTransactionId());
                    if (null == balanceInfo) {
                        listParentNode.add(transactionResDTO);
                    } else {
                        listNotParentNode.add(transactionResDTO);
                    }
                }
                if (TransactionConstants.INSTALL_TRANSACTION_CODES.contains(postingTransactionCode)) {
                    boolean flag3 = !StringUtils.isBlank(transactionResDTO.getInstallmentOrderId())
                            && "1".equals(transactionResDTO.getInstallmentIndicator());
                    if (flag3) {
                        listParentNode1.add(transactionResDTO);
                    } else {
                        listNotParentNode1.add(transactionResDTO);
                    }
                }
                if (!TransactionConstants.TRANSACTION_CODES.contains(postingTransactionCode)
                        && !TransactionConstants.PN_TRANSACTION_CODES.contains(postingTransactionCode)
                        && !TransactionConstants.INSTALL_TRANSACTION_CODES.contains(postingTransactionCode)) {
                    trans.add(transactionResDTO);
                }
            }
        }

        if (listParentNode.size() > 0) {
            for (PostedTransactionResDTO postedTransactionResDTO : listParentNode) {
                for (PostedTransactionResDTO transactionResDTO : listNotParentNode) {
                    if (transactionResDTO.getOriginalPostedTransactionId().equals(postedTransactionResDTO.getTransactionBalanceId())) {
                        List<PostedTransactionResDTO> notParentTransactions = new ArrayList<>();
                        notParentTransactions.add(transactionResDTO);
                        postedTransactionResDTO.setChildren(notParentTransactions);
                    }
                }
            }
        }
        List<PostedTransactionResDTO> transactionResDTOList = new ArrayList<>();
        List<PostedTransactionResDTO> transactionList = new ArrayList<>();
        if (listParentNode1.size() > 0) {
            for (PostedTransactionResDTO postedTransactionResDTO : listParentNode1) {
                for (PostedTransactionResDTO transactionResDTO : listNotParentNode1) {
                    if (transactionResDTO.getInstallmentOrderId().equals(postedTransactionResDTO.getInstallmentOrderId())
                            && "1".equals(transactionResDTO.getInstallmentIndicator())) {
                        List<PostedTransactionResDTO> notParentTransactions1 = new ArrayList<>();
                        notParentTransactions1.add(transactionResDTO);
                        postedTransactionResDTO.setChildren(notParentTransactions1);
                    } else {
                        transactionList.add(transactionResDTO);
                    }
                }
            }
            transactionResDTOList = transactionList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PostedTransactionResDTO::getPostedTransactionId))), ArrayList::new)
            );
        }

        //遍历展示层级关系
        List<PostedTransactionResDTO> postedTransactionResDTOS = useListPostedTransToTree(transactionResDTOS);

        postedTransactionResDTOS.addAll(listParentNode);
        postedTransactionResDTOS.addAll(listParentNode1);
        postedTransactionResDTOS.addAll(trans);
        if (listNotParentNode.size() > 0 && listParentNode.size() == 0) {
            postedTransactionResDTOS.addAll(listNotParentNode);
        }
        if (listNotParentNode1.size() > 0 && listParentNode1.size() == 0) {
            postedTransactionResDTOS.addAll(listNotParentNode1);
        }
        postedTransactionResDTOS.addAll(transactionResDTOList);

        List<PostedTransactionResDTO> newPostedTrans = new ArrayList<>(16);
        if (!CollectionUtils.isEmpty(postedTransactionResDTOS)){
            for (PostedTransactionResDTO postedTransactionResDTO : postedTransactionResDTOS) {
                String ifiIndicator = postedTransactionResDTO.getIfiIndicator();
                if ("PG005".equals(postedTransactionResDTO.getPostingTransactionCode())){
                    postedTransactionResDTO.setMerchantName(null);
                }
                if (!StringUtils.isEmpty(ifiIndicator)){
                    if (!"N".equals(ifiIndicator)){
                        newPostedTrans.add(postedTransactionResDTO);
                    }
                }else {
                    newPostedTrans.add(postedTransactionResDTO);
                }
            }
        }


        List<PostedTransactionResDTO> resultDtos = new ArrayList<>();
        int start = (page - 1) * rows;
        int end = newPostedTrans.size() < (page * rows) ? newPostedTrans.size() : (page * rows);
        for (int i = start; i < end; i++) {
            resultDtos.add(newPostedTrans.get(i));
        }

        buildInstallTermInfo(resultDtos);

        double totalPage = Math.ceil(newPostedTrans.size() / (double) rows);
        return new PageResultDTO<>(page, rows, newPostedTrans.size(), (int) totalPage, resultDtos);

    }

    private void buildInstallTermInfo(PostedTransactionDTO postedTransactionResDTO) {
        if (StringUtils.isNotBlank(postedTransactionResDTO.getInstallmentOrderId())) {
            InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(postedTransactionResDTO.getInstallmentOrderId());

            String orElse = Optional.ofNullable(postedTransactionResDTO.getInstallmentCurrentTerm()).orElse("");

            postedTransactionResDTO.setTransactionDescription(postedTransactionResDTO.getTransactionDescription()
                    + String.format("(%S%s)", StringUtils.isBlank(orElse) ? "" : orElse + "/", installOrder.getTerm()));
        }
    }

    private void buildInstallTermInfo(List<PostedTransactionResDTO> resultDtos) {
        if (CollectionUtils.isEmpty(resultDtos)) {
            return;
        }

        resultDtos.forEach(e -> {
            if (StringUtils.isNotBlank(e.getInstallmentOrderId()) ) {

                ParmTransactionCode parmTransactionCode = transactionCodeSelfMapper.selectByOrgNumberAndCode(
                        e.getOrganizationNumber(), e.getPostingTransactionCode());

                if (!TransactionConstants.INSTALL_ATTRIBUTE.contains(parmTransactionCode.getTransactionAttribute())){
                    return;
                }

                InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(e.getInstallmentOrderId());
                String orElse = Optional.ofNullable(e.getInstallmentCurrentTerm()).orElse("");
                e.setTransactionDescription(e.getTransactionDescription()
                        + String.format("(%S%s)", StringUtils.isBlank(orElse) ? "" : orElse + "/", installOrder.getTerm()));
            }

        });
    }

    @Override
    public PostedTransactionDTO findPostedTransactionById(String postedTransactionId) {
        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(postedTransactionId);
        if (postedTransaction == null) {
            logger.error("postedTransaction does not exist, postedTransactionId: {}", postedTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        PostedTransactionDTO postedTransactionDTO = BeanMapping.copy(postedTransaction, PostedTransactionDTO.class);
        List<PostedTranAccountRelationInfo> tranAccountRelationInfos = postedTranAccountRelationInfoSelfMapper.selectTransIdAndAmountByPostId(postedTransactionId, postedTransaction.getOrganizationNumber());
        List<PostedTranAccountRelationDTO> postedTranAccountRelations = new ArrayList<>();
        tranAccountRelationInfos.forEach(x -> {
            PostedTranAccountRelationDTO postedTranAccountRelationDTO = new PostedTranAccountRelationDTO();
            postedTranAccountRelationDTO.setTransactionBalanceId(x.getTransactionBalanceId());
            postedTranAccountRelationDTO.setAmount(x.getAmount());
            postedTranAccountRelations.add(postedTranAccountRelationDTO);
        });
        postedTransactionDTO.setPostedTranAccountRelations(postedTranAccountRelations);

        buildInstallTermInfo(postedTransactionDTO);
        return postedTransactionDTO;
    }

    @Override
    public PostedTransactionDTO modifyPostTrans(PostedTransactionDTO req) {
        req.setUpdateTime(LocalDateTime.now());
        req.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
        try {
            int res = postedTransactionMapper.updateByPrimaryKeySelective(BeanMapping.copy(req, PostedTransaction.class));
            if (res != 1) {
                logger.error("Failed to update table {} by primary key {}, expected affected rows: {}, actual affected rows: {}", "POSTED_TRANSACTION", req.getPostedTransactionId(), 1, res);
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_EFFECT_NUM_ERROR);
            }
        } catch (Exception e) {
            logger.error("Database update failed", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, e);
        }
        return req;
    }

    @Override
    public PageResultDTO<PostedTransactionResDTO> findNotPostedTransactions(PostedTransactionDTO req, int order) {
        String organizationNumber = OrgNumberUtils.getOrg();
        Map<String, ParmTransactionCode> transactionCodesAndDesc = getTransactionCodeMap(organizationNumber);

        List<PostedTransactionResDTO> postedTransactionResList = new ArrayList<>();
        List<PostedTransaction> postedTransactions;
        try {
            PostedTransaction mapperReq = new PostedTransaction();
            BeanUtils.copyProperties(req, mapperReq);
            postedTransactions = postedTransactionSelfMapper.selectByStatementIdPage(mapperReq);
        } catch (Exception e) {
            logger.error("Error querying unposted transactions by account management id", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, e);
        }

       /* CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(req.getCardNumber());
        if (Objects.isNull(cardAuthorizationInfo)) {
            logger.error("Error querying card authorization information by card number, card number: {}", req.getCardNumber());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_DATA_NOT_EXIST);
        }
        String relationshipIndicator = cardAuthorizationInfo.getRelationshipIndicator();
        String customerId = StringUtils.equals(relationshipIndicator, "P") ? cardAuthorizationInfo.getPrimaryCustomerId() : cardAuthorizationInfo.getSupplementaryCustomerId();*/

       //update by 2022/01/10 接口不传卡号
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectForUnpostTransactions(req.getAccountManagementId());
        if (ObjectUtils.isEmpty(accountManagementInfo)) {
            logger.error("Account management data does not exist for account management id: {}", req.getAccountManagementId());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        String customerId = accountManagementInfo.getCustomerId();
        CustomerAuthorizationInfo customerAuthorizationInfo =
                customerAuthorizationInfoSelfMapper.selectByCustomerId(organizationNumber, customerId);

        //2022-07-06公司客户无客户授权信息
        if (Objects.isNull(customerAuthorizationInfo)) {
            ParmAcctProductMainInfo parmAcctProductMainInfo = parmAcctProductMainInfoSelfMapper
                    .selectByOrgNumAndProNum(organizationNumber,
                            accountManagementInfo.getProductNumber());
            if(Objects.nonNull(parmAcctProductMainInfo)){
                String attribute = parmAcctProductMainInfo.getAttribute();
                boolean flag = !org.springframework.util.StringUtils.isEmpty(attribute)
                        && !StringUtils.equalsAny(attribute, "V","T");
                if (flag && !parmAcctProductMainInfo.getCorporateIndicator().equals("C")){
                    logger.error("Error querying customer authorization information by customer number, customer number: {}", customerId);
                    throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
                }
            }
        }
        String idType = Objects.isNull(customerAuthorizationInfo) ? "" :customerAuthorizationInfo.getIdType();
        String idNumber = Objects.isNull(customerAuthorizationInfo) ? "" :customerAuthorizationInfo.getIdNumber();

        //汇总未出账单的交易金额
        BigDecimal totalCreditAmout = BigDecimal.ZERO;
        BigDecimal totalDebitAmout = BigDecimal.ZERO;
        BigDecimal totalAmout =  BigDecimal.ZERO;
        BigDecimal totalInterestAmout = BigDecimal.ZERO;

        for (PostedTransaction postedTransaction : postedTransactions) {
            PostedTransactionResDTO postedTransactionRes = new PostedTransactionResDTO();
            BeanUtils.copyProperties(postedTransaction, postedTransactionRes);
            TransactionCodeResDTO paramTransactionCode = BeanMapping.copy(transactionCodesAndDesc.get(postedTransaction.getPostingTransactionCode()), TransactionCodeResDTO.class);;
            if (paramTransactionCode == null) {
                logger.error(
                        "Failed to get transaction code parameters: organization number: {}, transaction code: {}",
                        postedTransaction.getOrganizationNumber(),
                        postedTransaction.getPostingTransactionCode());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARM_TRANS_CODE_NOT_EXIST);
            }
            postedTransactionRes.setTransactionAttribute(
                    paramTransactionCode.getTransactionAttribute());

            if(paramTransactionCode.getDebitCreditIndicator().equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode())){
                totalDebitAmout  = totalDebitAmout.add(postedTransaction.getPostingAmount());
            }else if(paramTransactionCode.getDebitCreditIndicator().equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode())){
                totalCreditAmout = totalCreditAmout.add(postedTransaction.getPostingAmount());
            }
            if(StringUtils.isNoneBlank(paramTransactionCode.getTransactionAttribute())
                    && (paramTransactionCode.getTransactionAttribute().equals("A")) || paramTransactionCode.getTransactionAttribute().equals("B") && paramTransactionCode.getDebitCreditIndicator().equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode())){
                totalInterestAmout = totalInterestAmout.add(postedTransaction.getPostingAmount());

            }
            postedTransactionRes.setRepayIden("1");
            postedTransactionRes.setPostingTransactionCodeDesc(paramTransactionCode.getDescription());
            postedTransactionRes.setPostIndicator("1");
            postedTransactionRes.setCustomerIdType(idType);
            postedTransactionRes.setCustomerIdNumber(idNumber);
            postedTransactionResList.add(postedTransactionRes);
        }

        //中行POC临时关于分期、正反交易层级关系遍历处理
        List<PostedTransactionResDTO> trans = new ArrayList<>();

        List<PostedTransactionResDTO> listParentNode = new ArrayList<>();
        List<PostedTransactionResDTO> listNotParentNode = new ArrayList<>();

        List<PostedTransactionResDTO> listParentNode1 = new ArrayList<>();
        List<PostedTransactionResDTO> listNotParentNode1 = new ArrayList<>();

        List<PostedTransactionResDTO> transactionResDTOS = new ArrayList<>();
        for (PostedTransactionResDTO transactionResDTO : postedTransactionResList) {
            if (null != transactionResDTO) {
                String postingTransactionCode = transactionResDTO.getPostingTransactionCode();
                if (TransactionConstants.TRANSACTION_CODES.contains(postingTransactionCode)) {
                    transactionResDTOS.add(transactionResDTO);
                }
                if (TransactionConstants.PN_TRANSACTION_CODES.contains(postingTransactionCode)) {
                    AccountBalanceInfo balanceInfo = accountBalanceInfoMapper.selectByPrimaryKey(transactionResDTO.getOriginalPostedTransactionId());
                    if (null == balanceInfo) {
                        listParentNode.add(transactionResDTO);
                    } else {
                        listNotParentNode.add(transactionResDTO);
                    }
                }
                if (TransactionConstants.INSTALL_TRANSACTION_CODES.contains(postingTransactionCode)) {
                    boolean flag3 = !StringUtils.isBlank(transactionResDTO.getInstallmentOrderId())
                            && "1".equals(transactionResDTO.getInstallmentIndicator());
                    if (flag3) {
                        listParentNode1.add(transactionResDTO);
                    } else {
                        listNotParentNode1.add(transactionResDTO);
                    }
                }
                if (!TransactionConstants.TRANSACTION_CODES.contains(postingTransactionCode)
                        && !TransactionConstants.PN_TRANSACTION_CODES.contains(postingTransactionCode)
                        && !TransactionConstants.INSTALL_TRANSACTION_CODES.contains(postingTransactionCode)) {
                    trans.add(transactionResDTO);
                }
            }
        }

        if (listParentNode.size() > 0) {
            for (PostedTransactionResDTO postedTransactionResDTO : listParentNode) {
                List<PostedTransactionResDTO> notParentTransactions = new ArrayList<>();
                for (PostedTransactionResDTO transactionResDTO : listNotParentNode) {
                    if (transactionResDTO.getOriginalPostedTransactionId().equals(postedTransactionResDTO.getTransactionBalanceId())) {
                        notParentTransactions.add(transactionResDTO);
                    }
                }
                postedTransactionResDTO.setChildren(notParentTransactions);
            }
        }
        List<PostedTransactionResDTO> transactionResDTOList = new ArrayList<>();
        if (listParentNode1.size() > 0) {
            for (PostedTransactionResDTO postedTransactionResDTO : listParentNode1) {
                List<PostedTransactionResDTO> notParentTransactions1 = new ArrayList<>();
                for (PostedTransactionResDTO transactionResDTO : listNotParentNode1) {
                    if (transactionResDTO.getInstallmentOrderId().equals(postedTransactionResDTO.getInstallmentOrderId())
                            && "1".equals(transactionResDTO.getInstallmentIndicator())) {
                        notParentTransactions1.add(transactionResDTO);
                    } else {
                        transactionResDTOList.add(transactionResDTO);
                    }
                }
                postedTransactionResDTO.setChildren(notParentTransactions1);
            }
        }

        //遍历展示层级关系
        List<PostedTransactionResDTO> postedTransactionResDTOS = useListPostedTransToTree(transactionResDTOS);

        postedTransactionResDTOS.addAll(listParentNode);
        postedTransactionResDTOS.addAll(listParentNode1);
        postedTransactionResDTOS.addAll(trans);
        if (listNotParentNode.size() > 0 && listParentNode.size() == 0) {
            postedTransactionResDTOS.addAll(listNotParentNode);
        }
        if (listNotParentNode1.size() > 0 && listParentNode1.size() == 0) {
            postedTransactionResDTOS.addAll(listNotParentNode1);
        }
        postedTransactionResDTOS.addAll(transactionResDTOList);

        List<PostedTransactionResDTO> postedTransactionResDTOS2 = new ArrayList<>();
        List<PostedTransactionResDTO> newPostedTrans = new ArrayList<>(16);
        if (!CollectionUtils.isEmpty(postedTransactionResDTOS)){
            for (PostedTransactionResDTO postedTransactionResDTO : postedTransactionResDTOS) {
                String ifiIndicator = postedTransactionResDTO.getIfiIndicator();
                if (!StringUtils.isEmpty(ifiIndicator)){
                    if (!"N".equals(ifiIndicator)){
                        newPostedTrans.add(postedTransactionResDTO);
                    }
                }else {
                    newPostedTrans.add(postedTransactionResDTO);
                }
            }
        }
        //遍历层级关系
//        List<PostedTransactionResDTO> postedTransactionResDtos = useListPostedTransToTree(postedTransactionResList);
        if (order == 1) {
            postedTransactionResDTOS2 = newPostedTrans.stream().sorted(Comparator.comparing(PostedTransactionResDTO::getPostingDate, Comparator.reverseOrder()).thenComparing(PostedTransactionResDTO::getTransactionDate, Comparator.reverseOrder()).thenComparing(PostedTransactionResDTO::getPostedTransactionId, Comparator.reverseOrder())).collect(Collectors.toList());
        } else if (order == 2) {
            //根据cardNow的要求添加已授权未入账的账单明细
            List<String> cardNumberList = new ArrayList<>(3);
            if (StringUtils.isNotEmpty(req.getCardNumber())) {
                cardNumberList.add(req.getCardNumber());
            } else {
                //根据管理账户查询客户下对应卡的所有交易
                if (LiabilityEnum.CORPORATE.getCode().equals(accountManagementInfo.getLiability())) {
                    //公司卡使用公司客户号查询
                    cardNumberList = cardAuthorizationInfoSelfMapper.selectByCorpIdAndAcctProduct(accountManagementInfo.getCustomerId(), accountManagementInfo.getProductNumber(), organizationNumber);
                } else {
                    //个人卡
                    cardNumberList = cardAuthorizationInfoSelfMapper.selectByCustIdAndAcctProduct(accountManagementInfo.getCustomerId(), accountManagementInfo.getProductNumber(), organizationNumber);
                }
            }
            //查询未并账交易中postFlag和transactionStatus均为0的数据
            List<OutstandingTransaction> outstandingTransactions = CollectionUtils.isNotEmpty(cardNumberList) ? outstandingTransactionSelfMapper.selectUnPostByCardNumberList(cardNumberList) : null;
            List<CardAuthorizationInfo>  cardAuthorizationInfos  = CollectionUtils.isNotEmpty(cardNumberList) ? cardAuthorizationInfoSelfMapper.selectByCardNumbers(cardNumberList):null;
            Map<String, String> cardProductNumberMap =  CollectionUtils.isNotEmpty(cardAuthorizationInfos) ?cardAuthorizationInfos.stream().collect(Collectors.toMap(CardAuthorizationInfo::getCardNumber, CardAuthorizationInfo::getProductNumber)):null;
            if (CollectionUtils.isNotEmpty(outstandingTransactions)) {
                List<PostedTransactionResDTO> postedTransactionResDTOTransList = outstandingTransactions.stream().map(outstandingTransaction -> {
                    PostedTransactionResDTO postedTransactionResDTOTrans = new PostedTransactionResDTO();
                    postedTransactionResDTOTrans.setAccountManagementId(req.getAccountManagementId());
                    postedTransactionResDTOTrans.setCardNumber(outstandingTransaction.getCardNumber());
                    postedTransactionResDTOTrans.setGlobalFlowNumber(outstandingTransaction.getGlobalFlowNumber());
                    postedTransactionResDTOTrans.setOriginalGlobalFlowNumber(outstandingTransaction.getOriginalGlobalFlowNumber());
                    postedTransactionResDTOTrans.setOrganizationNumber(organizationNumber);
                    postedTransactionResDTOTrans.setProductNumber(outstandingTransaction.getProductNumber());
                    postedTransactionResDTOTrans.setAccountProductNumber(accountManagementInfo.getProductNumber());//账产品
                    postedTransactionResDTOTrans.setCustomerId(outstandingTransaction.getCustomerId());
                    postedTransactionResDTOTrans.setPostingTransactionCode(outstandingTransaction.getPostingTransactionCode());
                    postedTransactionResDTOTrans.setDebitCreditIndcator(outstandingTransaction.getDebitCreditIndcator());
                    postedTransactionResDTOTrans.setTransactionSource(outstandingTransaction.getTransactionSource());
                    postedTransactionResDTOTrans.setTransactionAmount(outstandingTransaction.getTransactionAmount());
                    postedTransactionResDTOTrans.setTransactionCurrencyCode(outstandingTransaction.getTransactionCurrencyCode());
                    postedTransactionResDTOTrans.setTransactionDate(outstandingTransaction.getTransactionTime());
                    postedTransactionResDTOTrans.setPostingAmount(outstandingTransaction.getBillingAmount());
                    postedTransactionResDTOTrans.setPostingCurrencyCode(outstandingTransaction.getBillingCurrencyCode());
                    postedTransactionResDTOTrans.setAuthorizationCode(outstandingTransaction.getAuthorizationCode());
                    postedTransactionResDTOTrans.setRetrievalReferenceNumber(outstandingTransaction.getRetrievalReferenceNumber());
                    postedTransactionResDTOTrans.setTransactionDescription(outstandingTransaction.getTransactionDescription());
                    postedTransactionResDTOTrans.setCountryCode(outstandingTransaction.getCountryCode());
                    postedTransactionResDTOTrans.setMerchantId(outstandingTransaction.getMerchantId());
                    postedTransactionResDTOTrans.setMerchantName(outstandingTransaction.getMerchantName());
                    postedTransactionResDTOTrans.setMcc(outstandingTransaction.getMcc());
                    postedTransactionResDTOTrans.setSecondMerchantId(outstandingTransaction.getSecondMerchantId());
                    postedTransactionResDTOTrans.setSecondMerchantName(outstandingTransaction.getSecondMerchantName());
                    postedTransactionResDTOTrans.setSettlementCurrencyCode(outstandingTransaction.getSettlementCurrencyCode());
                    postedTransactionResDTOTrans.setSettlementAmount(outstandingTransaction.getSettlementAmount());
                    postedTransactionResDTOTrans.setPosEntryMode(outstandingTransaction.getPosEntryMode());
                    postedTransactionResDTOTrans.setReimbursementAttribute(outstandingTransaction.getReimbursementAttribute());
                    postedTransactionResDTOTrans.setFallbackIndicator(outstandingTransaction.getFallbackIndicator());
                    postedTransactionResDTOTrans.setInstallmentIndicator(outstandingTransaction.getInstallmentIndicator());
                    postedTransactionResDTOTrans.setInstallmentOrderId(outstandingTransaction.getInstallmentOrderId());
                    postedTransactionResDTOTrans.setOpponentBankId(outstandingTransaction.getOpponentBankId());
                    postedTransactionResDTOTrans.setOpponentAccountNumber(outstandingTransaction.getOpponentAccountNumber());
                    postedTransactionResDTOTrans.setOpponentAccountName(outstandingTransaction.getOpponentAccountName());
                    postedTransactionResDTOTrans.setCustomerIdType(idType);
                    postedTransactionResDTOTrans.setCustomerIdNumber(idNumber);
                    postedTransactionResDTOTrans.setCreateTime(outstandingTransaction.getCreateTime());
                    postedTransactionResDTOTrans.setUpdateTime(outstandingTransaction.getUpdateTime());
                    postedTransactionResDTOTrans.setUpdateBy(outstandingTransaction.getUpdateBy());
                    postedTransactionResDTOTrans.setMessageIndicator(outstandingTransaction.getMessageIndicator());
                    postedTransactionResDTOTrans.setMerchantInfo2ndCode(outstandingTransaction.getMerchantInfo2ndSort());
                    postedTransactionResDTOTrans.setPostingTransactionCodeDesc(Optional.ofNullable(transactionCodesAndDesc.get(postedTransactionResDTOTrans.getPostingTransactionCode())).orElse(new ParmTransactionCode()).getDescription());
                    //如果取不到入账交易码的描述，就取授权交易码的描述
                    if (StringUtils.isBlank(postedTransactionResDTOTrans.getPostingTransactionCodeDesc())) {
                        postedTransactionResDTOTrans.setPostingTransactionCodeDesc(authTransactionCodeDesc.get(outstandingTransaction.getAuthorizationTransactionCode()));
                    }
                    postedTransactionResDTOTrans.setPostIndicator("0");
                    return postedTransactionResDTOTrans;
                }).collect(Collectors.toList());
                newPostedTrans.addAll(postedTransactionResDTOTransList);
            }
            postedTransactionResDTOS2 = newPostedTrans.stream().sorted(Comparator.comparing(PostedTransactionResDTO::getTransactionDate, Comparator.reverseOrder())).collect(Collectors.toList());
        }
        /* TODO 暂时注释
        Integer page = req.getPage();
        Integer rows = req.getRows();
        List<PostedTransactionResDTO> resultDtos = new ArrayList<>();
        int start = (page - 1) * rows;
        int end = Math.min(newPostedTrans.size(),
                (page * rows));
        for (int i = start; i < end; i++) {
            resultDtos.add(postedTransactionResDTOS2.get(i));
        }

        buildInstallTermInfo(resultDtos);

        //biz-common工程transaction间接依赖 故不在page信息里单独封装

        LocalDate lastStatementDate = accountManagementInfo.getLastStatementDate();
        if(null == accountManagementInfo.getLastStatementDate() ||
                LocalDate.of(1,1,1).compareTo(accountManagementInfo.getLastStatementDate()) ==0){
            totalAmout = totalDebitAmout.subtract(totalCreditAmout);
        }else{
            AccountStatementInfo accountStatementInfo = statementInfoSelfMapper.selectByAccountManagementIdAndDate(accountManagementInfo.getAccountManagementId(), accountManagementInfo.getLastStatementDate());
            BigDecimal closeBalance = accountStatementInfo == null ? BigDecimal.ZERO : accountStatementInfo.getCloseBalance();
            totalAmout = Optional.ofNullable(closeBalance).orElse(BigDecimal.ZERO).add(totalDebitAmout).subtract(totalCreditAmout);
        }
        if(CollectionUtils.isNotEmpty(resultDtos)){
            PostedTransactionResDTO param = resultDtos.get(0);
            if(Objects.nonNull(param)){
                logger.info("Total debit amount:{} Total credit amount:{} Total amount:{}",totalDebitAmout,totalCreditAmout,totalAmout);
                param.setTotalCreditAmout(totalCreditAmout);
                param.setTotalDebitAmout(totalDebitAmout);
                param.setTotalAmout(totalAmout);
                param.setTotalInterestAmout(totalInterestAmout);
            }
        }

        return new PageResultDTO<>(
                page, rows, postedTransactionResDTOS2.size(),
                postedTransactionResDTOS2.size() / rows + ((postedTransactionResDTOS2.size() % rows) > 0 ? 1 : 0),
                resultDtos.stream().distinct().collect(Collectors.toList()));
         */
        return null;
    }

    private Map<String, ParmTransactionCode> getTransactionCodeMap(String organizationNumber) {
        //查询并准备交易码描述的数据
        List<ParmTransactionCode> transactionCodes = transactionCodeSelfMapper.selectAll(organizationNumber);
        if (CollectionUtils.isEmpty(transactionCodes)) {
            logger.error("Error querying transaction code information by organization number, organization number: {}", organizationNumber);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        return transactionCodes.stream()
                .collect(Collectors.toMap(ParmTransactionCode::getTransactionCode, parmTransactionCode -> parmTransactionCode));
    }

    /**
     * 将list转为树tree结构
     *
     * @param allPostedTrans 已入账交易明细list
     * @return PostedTransaction
     */
    private List<PostedTransactionResDTO> useListPostedTransToTree(List<PostedTransactionResDTO> allPostedTrans) {
        List<PostedTransactionResDTO> listParentNode = new ArrayList<>();
        List<PostedTransactionResDTO> listNotParentNode = new ArrayList<>();
        //遍历allPostedTrans保存所有数据的postedTransactionId用于判断是不是根节点
        Map<String, String> mapAllPostedTransId = new HashMap<>(16);
        if (allPostedTrans != null) {
            for (PostedTransactionResDTO postedTransactionResDTO : allPostedTrans) {
                mapAllPostedTransId.put(postedTransactionResDTO.getPostedTransactionId(), postedTransactionResDTO.getPostedTransactionId());
                mapAllPostedTransId.put(postedTransactionResDTO.getTransactionBalanceId(), postedTransactionResDTO.getTransactionBalanceId());
                mapAllPostedTransId.put(postedTransactionResDTO.getInstallmentOrderId(), postedTransactionResDTO.getInstallmentOrderId());
            }
        }
        //遍历allPostedTrans找出所有的根节点和非根节点
        if (allPostedTrans != null && !allPostedTrans.isEmpty()) {
            for (PostedTransactionResDTO postedTransactionResDTO : allPostedTrans) {
                boolean flag1 = StringUtils.isBlank(postedTransactionResDTO.getParentPostedTransactionId())
                        || !mapAllPostedTransId.containsKey(postedTransactionResDTO.getParentPostedTransactionId());
                if (flag1) {
                    listParentNode.add(postedTransactionResDTO);
                } else {
                    listNotParentNode.add(postedTransactionResDTO);
                }
            }
        }
        //递归获取所有子节点
        if (!listParentNode.isEmpty()) {
            for (PostedTransactionResDTO postedTransactionResDTO : listParentNode) {
                //添加所有子级
                postedTransactionResDTO.setChildren(
                        this.getTreeChildPostedTrans(listNotParentNode,
                                postedTransactionResDTO.getPostedTransactionId()));
            }
        }
        return listParentNode;
    }

    /**
     * 递归查询子节点
     *
     * @param childList 子节点
     * @param parentId  父节点id
     * @return List<PostedTransactionResDTO>
     */
    private List<PostedTransactionResDTO> getTreeChildPostedTrans(
            List<PostedTransactionResDTO> childList, String parentId) {
        List<PostedTransactionResDTO> listParentNode = new ArrayList<>();
        List<PostedTransactionResDTO> listNotParentNode = new ArrayList<>();
        //遍历childList，找出所有的根节点和非根节点
        if (childList != null && !childList.isEmpty()) {
            for (PostedTransactionResDTO postedTransactionResDTO : childList) {
                //对比找出父节点
                if (StringUtils.equals(postedTransactionResDTO.getParentPostedTransactionId(), parentId)) {
                    listParentNode.add(postedTransactionResDTO);
                } else {
                    listNotParentNode.add(postedTransactionResDTO);
                }
            }
        }
        //查询子节点
        if (!listParentNode.isEmpty()) {
            for (PostedTransactionResDTO postedTransactionResDTO : listParentNode) {
                //递归查询子节点
                postedTransactionResDTO.setChildren(getTreeChildPostedTrans(listNotParentNode, postedTransactionResDTO.getPostedTransactionId()));
            }
        }
        return listParentNode;
    }

    @Override
    public void updatePostedTransactionByBatch(PostedTransactionBatchDTO postedTransactionBatchDTO) {
        PostedTransaction transaction = new PostedTransaction();
        transaction.setStatementDate(postedTransactionBatchDTO.getStatementDate());
        transaction.setAccountStatementId(postedTransactionBatchDTO.getAccountStatementId());
        transaction.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
        transaction.setUpdateTime(LocalDateTime.now());
        transaction.setAccountManagementId(postedTransactionBatchDTO.getAccountManagementId());
        postedTransactionSelfMapper.updateByBatch(transaction, postedTransactionBatchDTO.getLastAccruedThruDay(),
                postedTransactionBatchDTO.getAccruedThruDay());
    }

    @Override
    public List<PostedTransactionDTO> findBycardNumber(String cardNumber) {
        if (cardNumber == null) {
            logger.error("Card number cannot be empty, cardNumber");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_CARDNUM_IS_EMPTY);
        }
        return BeanMapping.copyList(postedTransactionSelfMapper.findBycardNumber(cardNumber), PostedTransactionDTO.class);
    }

    @Override
    public List<PostedTransactionDTO> findByaccountManagementId(String accountManagementId) {
        if (accountManagementId == null) {
            logger.error("Account cannot be empty, accountManagementId");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_ID_IS_EMPTY);
        }
        return BeanMapping.copyList(postedTransactionSelfMapper.selectByAccStaId(accountManagementId, OrgNumberUtils.getOrg()), PostedTransactionDTO.class);
    }

    /**
     * 上面的根据管理账户查询实际sql使用的查询条件是根据ACCOUNT_STATEMENT_ID查询
     *
     * @param accountManagementId 账户
     * @return
     */
    @Override
    public List<PostedTransactionDTO> findByaccountManagementIdCc(String accountManagementId) {
        if (accountManagementId == null) {
            logger.error("Account cannot be empty, accountManagementId");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_ID_IS_EMPTY);
        }
        return BeanMapping.copyList(postedTransactionSelfMapper.selectByAccountManagementId(accountManagementId, OrgNumberUtils.getOrg()), PostedTransactionDTO.class);
    }

    @Override
    public Integer updateIndicatorByOriginTransactionId(String installmentIndicator, String originTransactionId) {
        if (installmentIndicator == null || "".equals(installmentIndicator)) {
            logger.error("Installment transaction flag cannot be empty, installmentIndicator: {}", installmentIndicator);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_INSTALLMENT_INDICATOR_IS_EMPTY);
        }
        if (originTransactionId == null || "".equals(originTransactionId)) {
            logger.error("Original transaction id cannot be empty, originTransactionId: {}", originTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_TRANS_ID_IS_EMPTY);
        }
        int i = postedTransactionSelfMapper.updateIndicatorByOriginTransactionId(installmentIndicator, originTransactionId);
        if (i <= 0) {
            logger.error("Failed to modify by original transaction, originTransactionId: {}", originTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return i;
    }

    /**
     * 累加入账金额，根据卡号、管理账号、账单账户id
     */
    @Override
    public BigDecimal sumPostingAmout(String cardNumber, String accountManagementId, String accountStatementId) {
        Map<String, BigDecimal> resultMap = postedTransactionSelfMapper.sumPostingAmout(cardNumber, accountManagementId, accountStatementId);
        return resultMap.get("postingAmount");
    }

    /**
     * 批量累加入账金额，根据卡号、管理账号、账单账户id
     */
    @Override
    public List<Map<String, Object>> batchSumPostingAmout(List<Map<String, String>> paramList) {
        return postedTransactionSelfMapper.batchSumPostingAmout(paramList);
    }

    /**
     * 根据账号、账单账户id查询账单列表
     *
     * @param accountManagementId 账号
     * @param accountStatementId  账单账户id
     * @return PostedTransactionResDTO
     */
    @Override
    public List<PostedTransactionResDTO> findByAccountManagementIdAndAccountStatementId(
            String accountManagementId, String accountStatementId) {
        logger.info("Query posted transactions by account management id and account statement id");
        if (accountManagementId == null || "".equals(accountManagementId)) {
            logger.error("Account cannot be empty, accountManagementId");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_MANAGE_ID_IS_EMPTY);
        }
        if (accountStatementId == null || "".equals(accountStatementId)) {
            logger.error("Account statement id cannot be empty, accountStatementId");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ACC_STATEMENT_ID_IS_EMPTY);
        }
        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByMidAndSid(accountManagementId, accountStatementId);
        return BeanMapping.copyList(postedTransactions, PostedTransactionResDTO.class);
    }

    /**
     * 指定交易账户还款
     *
     * @param designatedRepaymentReqBO 页面信息
     * @return boolean
     */
    @Override
    public boolean designatedTransRepayment(DesignatedRepaymentReqBO designatedRepaymentReqBO) {
        RecordedBO recorded = buildRecorded(designatedRepaymentReqBO);
        txnRecordedService.txnRecorded(recorded);
        return true;
    }

    /**
     * 根据机构号、已入账id，查询已入账交易信息表
     *
     * @param orgNumber            机构号
     * @param transactionBalanceId 交易id
     * @return PostedTransaction
     */
    @Override
    public List<PostedTransactionDTO> getByOrgAndBid(String orgNumber, String transactionBalanceId) {
        // TODO 此方法没有用到，临时赋值null
        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByOrgAndBid(orgNumber, transactionBalanceId, null);
        if (postedTransactions != null) {
            return BeanMapping.copyList(postedTransactions, PostedTransactionDTO.class);
        }
        return null;
    }

    /**
     * @param designatedRepaymentReqBO 请求参数
     * @return Recorded
     */
    private RecordedBO buildRecorded(DesignatedRepaymentReqBO designatedRepaymentReqBO) {
        RecordedBO recorded = new RecordedBO();
        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(designatedRepaymentReqBO.getPostedTransactionId());
        String newTransCode = designatedRepaymentReqBO.getTransactionCode();
        BigDecimal amount = designatedRepaymentReqBO.getAmount();
        //管理账户Id
        recorded.setTxnAccountManageId(postedTransaction.getAccountManagementId());
        //调整交易的原交易余额信息id
//        recorded.setTxnOriginalTransactionBalanceId(postedTransaction.getTransactionBalanceId());
        recorded.setTxnOriginalPostedTransactionId(designatedRepaymentReqBO.getPostedTransactionId());
        //卡号,初始化0
        recorded.setTxnCardNumber(postedTransaction.getCardNumber());
        //全局业务流水号
        recorded.setTxnGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        //入账方式
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        //拒绝重入账标志
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        //冲减交易费用标识
        recorded.setTxnReverseFeeIndicator(ReverseFeeIndicatorEnum.NO.getCode());
        //交易码
        recorded.setTxnTransactionCode(newTransCode);
        //交易来源
        recorded.setTxnTransactionSource(TransactionSourceEnum.INNER_FEE.getCode());
        TransactionCodeResDTO paramTransactionCode = transactionCodeService.findTransactionCode(postedTransaction.getOrganizationNumber(), newTransCode);
        if (paramTransactionCode != null) {
            //交易描述
            recorded.setTxnTransactionDescription(paramTransactionCode.getDescription());
        }
        // 入账日期 and 交易日期由入账逻辑赋值
        //交易金额
        recorded.setTxnTransactionAmount(amount);
        //交易币种,交易级账户的币种（currency）
        recorded.setTxnTransactionCurrency(postedTransaction.getPostingCurrencyCode());
        //入账金额
        recorded.setTxnBillingAmount(amount);
        //入账币种
        recorded.setTxnBillingCurrency(postedTransaction.getPostingCurrencyCode());
        //清算金额
        recorded.setTxnSettlementAmount(amount);
        //清算币种
        recorded.setTxnSettlementCurrency(postedTransaction.getPostingCurrencyCode());
        //商户编号
        recorded.setTxnMerchantId(postedTransaction.getMerchantId());
        //商户名称
        recorded.setTxnMerchantName(postedTransaction.getMerchantName());
        //MCC
        recorded.setTxnMerchantCategoryCode(postedTransaction.getMcc());
        //国家码
        recorded.setTxnCountryCode(postedTransaction.getCountryCode());
        //省份/州
        recorded.setTxnStateCode(postedTransaction.getStateCode());
        //城市
        recorded.setTxnCityCode(postedTransaction.getCityCode());
        //授权匹配标志,0=未匹配授权
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //是否恢复授权占用额度标志,N:不需要
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        //强制入账标识
        recorded.setTxnForcePostIndicator("0");
        return recorded;
    }


    @Override
    public Integer updateInstAmountByPostId(BigDecimal instAmount, String installmentIndicator, String originTransactionId) {
        if (StringUtils.isEmpty(originTransactionId)) {
            logger.error("Original transaction id cannot be empty, originTransactionId: {}", originTransactionId);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_ORI_TRANS_ID_IS_EMPTY);
        }
        int i = postedTransactionSelfMapper.updateInstAmountByPostId(instAmount, installmentIndicator, originTransactionId);
        if (i <= 0) {
            logger.error("Failed to modify by original transaction");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return i;
    }

    @Override
    public List<PostedTransactionDTO> getByPostingDate(String transactionSource, LocalDate postingDate) {
        List<PostedTransactionDTO> postedTransactionDTOList = new ArrayList<>();
        try {
            List<PostedTransaction> postedTransactionList = postedTransactionSelfMapper.selectBySourceAndPosDate(transactionSource, postingDate);
            postedTransactionDTOList = BeanMapping.copyList(postedTransactionList, PostedTransactionDTO.class);
        } catch (Exception e) {
            logger.error("Error querying getByPostingDate", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return postedTransactionDTOList;
    }

    @Override
    public List<PostedTransactionDTO> getLocalByPostingDate(LocalDate postingDate) {
        List<PostedTransactionDTO> postedTransactionDTOList = new ArrayList<>();
        try {
            List<PostedTransaction> postedTransactionList = postedTransactionSelfMapper.selectByLocalSource(postingDate);
            postedTransactionDTOList = BeanMapping.copyList(postedTransactionList, PostedTransactionDTO.class);
        } catch (Exception e) {
            logger.error("Error querying getLocalByPostingDate", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return postedTransactionDTOList;
    }

    @Override
    public List<PostedTransactionDTO> getByPostingDateAndOther(LocalDate postingDate) {
        List<PostedTransactionDTO> postedTransactionDTOList = new ArrayList<>();
        try {
            List<PostedTransaction> postedTransactionList = postedTransactionSelfMapper.selectByPosDateAndOther(postingDate);
            postedTransactionDTOList = BeanMapping.copyList(postedTransactionList, PostedTransactionDTO.class);
        } catch (Exception e) {
            logger.error("Error querying getByPostingDateAndOther", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return postedTransactionDTOList;
    }

    @Override
    public List<PostedTransactionDTO> getByUnilaterInSettle() {
        List<PostedTransactionDTO> postedTransactionDTOList = new ArrayList<>();
        try {
            List<PostedTransaction> postedTransactionList = postedTransactionSelfMapper.selectByUnilaterInSettle();
            postedTransactionDTOList = BeanMapping.copyList(postedTransactionList, PostedTransactionDTO.class);
        } catch (Exception e) {
            logger.error("Error querying getByUnilaterInSettle", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return postedTransactionDTOList;
    }

    @Override
    public List<PostedTransactionDTO> getByUnilaterNotInSettle() {
        List<PostedTransactionDTO> postedTransactionDTOList;
        try {
            List<PostedTransaction> postedTransactionList = postedTransactionSelfMapper.selectByUnilaterNotInSettle();
            postedTransactionDTOList = BeanMapping.copyList(postedTransactionList, PostedTransactionDTO.class);
        } catch (Exception e) {
            logger.error("Error querying getByUnilaterNotInSettle", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR);
        }
        return postedTransactionDTOList;
    }
    @Override
    public PageResultDTO<PostedTransactionResDTO> findCorpCardTrans(PostedTransactionDTO req) {

        String organizationNumber = req.getOrganizationNumber();
        String orgNumber = OrgNumberUtils.getOrg(organizationNumber);
        String corporateCustomerId = req.getCorporateCustomerId();
        String cardNumber = req.getCardNumber();

        List<PostedTransaction> transactionsList = null;
        List<PostedTransactionResDTO> postedTransactionResDTOS;

        try {

            List<PostedTransactionDTO> postedTransactionBOArrayList = Lists.newArrayList(req);
            List<PostedTransaction> postedTransactionList = BeanMapping.copyList(postedTransactionBOArrayList, PostedTransaction.class);

            if (StringUtils.isNotBlank(cardNumber)) {
                // 公司卡账户
                transactionsList = postedTransactionSelfMapper.selectTransByCorCustomerId(postedTransactionList);
            }else {
                // 公司卡结构中 虚拟账户的兼容
                Set<String> productNumberList = new HashSet<>();
                List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByCustomerId(orgNumber, corporateCustomerId);
                for (AccountManagementInfo accountManagementInfo : accountManagementInfoList) {
                    String productNumber = accountManagementInfo.getProductNumber();
                    productNumberList.add(productNumber);
                }
                // 逻辑校验
                if (CollectionUtils.isNotEmpty(productNumberList)) {
                    List<String> vaTaTypeList = Lists.newArrayList(VaTaTypeEnum.VA0001.getCode(), VaTaTypeEnum.TA0001.getCode());
                    String midProductNumber = null;
                    for (String item : vaTaTypeList) {
                        for (String productNumber : productNumberList) {
                            if (item.equals(productNumber)) {
                                midProductNumber = item;
                                break;
                            }
                            break;
                        }
                    }
                    ParmAcctProductMainInfo parmAcctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(orgNumber, midProductNumber);
                    if (parmAcctProductMainInfo != null) {
                        if (parmAcctProductMainInfo.getAttribute().equals("V") || parmAcctProductMainInfo.getAttribute().equals("T")) {
                            transactionsList = postedTransactionSelfMapper.selectTransByCustomerId(postedTransactionList);
                        }
                    }
                }
            }

            postedTransactionResDTOS = BeanMapping.copyList(transactionsList, PostedTransactionResDTO.class);

        } catch (Exception e) {
            logger.error("Failed to query transactions by card number {} {}", req.getCardNumber(), e.getMessage());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST, e);
        }
        /*
        Integer page = req.getPage();
        Integer rows = req.getRows();
        List<PostedTransactionResDTO> resultDtos = new ArrayList<>();
        int start = (page - 1) * rows;
        int end = Math.min(postedTransactionResDTOS.size(), (page * rows));
        for (int i = start; i < end; i++) {
            resultDtos.add(postedTransactionResDTOS.get(i));
        }
        return new PageResultDTO<>(page, rows, postedTransactionResDTOS.size(),
                postedTransactionResDTOS.size() / rows + ((postedTransactionResDTOS.size() % rows) > 0 ? 1 : 0),
                resultDtos.stream().distinct().collect(Collectors.toList()));

         */
        return null;
    }

    @Override
    public PartnerNotPostTransactionRespDTO findNotPostedTransactionsToken(PostedTransaction req) {
        String organizationNumber = OrgNumberUtils.getOrg();
        Map<String, ParmTransactionCode> transactionCodesAndDesc = getTransactionCodeMap(organizationNumber);

        if (StringUtils.isAllBlank(req.getCardNumber(), req.getAccountManagementId())) {
            logger.error("Token query unposted transactions, card number and account management number are both empty!");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARAMETER_IS_NULL);
        }

        if (StringUtils.isBlank(req.getAccountManagementId())) {
            req.setAccountManagementId(findMidByCardNumber(req.getCardNumber()));
        }
/* TODO 暂时注释

        List<PostedTransactionResDTO> postedTransactionResList = new ArrayList<>();
        PostedTransaction mapperReq = new PostedTransaction();
        BeanUtils.copyProperties(req, mapperReq);
        if (Objects.nonNull(req.getTransactionDateStart())) {
            mapperReq.setTransactionDateStart(req.getTransactionDateStart().atTime(0, 0, 0, 0));
        }
        if (Objects.nonNull(req.getTransactionDateEnd())) {
            mapperReq.setTransactionDateEnd(req.getTransactionDateEnd().plusDays(1).atTime(0, 0, 0, 0));
        }

        try {
            postedTransactionResList = postedTransactionSelfMapper.selectByNotPosted(mapperReq, req.getRows(), req.getRows() * (req.getPage() - 1));
        } catch (Exception e) {
            logger.error("Error querying unposted transactions by account management id", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_DATA_NOT_EXIST, e);
        }

        List<PostedTransactionResDTO> resultList = new ArrayList<>(16);

        postedTransactionResList.forEach(p -> {
            switch (p.getPostingTransactionCode()) {
                case "R":
                    p.setTransactionAttribute(TransactionAttributeEnum.CONSUME.getCode());
                    break;
                case "C":
                    p.setTransactionAttribute(TransactionAttributeEnum.CASH.getCode());
                    break;
                case "P":
                    p.setTransactionAttribute(TransactionAttributeEnum.REPAYMENT.getCode());
                    break;
                case "I":
                    p.setTransactionAttribute(TransactionAttributeEnum.INSTALL_PRINCIPAL.getCode());
                    break;
                default:
                    ParmTransactionCode transactionCode = transactionCodesAndDesc.getOrDefault(p.getPostingTransactionCode(), null);
                    if (Objects.nonNull(transactionCode)) {
                        p.setTransactionAttribute(transactionCode.getTransactionAttribute());
                        p.setPostingTransactionCodeDesc(transactionCode.getDescription());
                        //年费交易特殊处理
                        if (TransactionConstants.ANNUAL_FEE_TRXN_CODE.contains(transactionCode.getTransactionCode())) {
                            p.setTransactionAttribute("N");
                        } else if (TransactionConstants.PHYSICAL_CARD_FEE_TRXN_CODE.contains(transactionCode.getTransactionCode())) {
                            p.setTransactionAttribute("P");
                        } else if (TransactionConstants.REPLACE_CARD_FEE_TRXN_CODE.contains(transactionCode.getTransactionCode())) {
                            p.setTransactionAttribute("R");
                        }
                    }
            }
            if (!"N".equals(p.getIfiIndicator())) {
                resultList.add(p);
            }
        });

        List<PartnerNotPostTransactionSumDTO> sumPageList = postedTransactionSelfMapper.selectTokenUnpostPageInfo(mapperReq);
        Integer totalUnderCondition = sumPageList.stream().map(PartnerNotPostTransactionSumDTO::getTotalCount).reduce(0, Integer::sum);

        Integer page = req.getPage();
        Integer rows = req.getRows();

        buildInstallTermInfo(resultList);

        List<PartnerNotPostTransactionSumDTO> sumList = postedTransactionSelfMapper.selectSumByNotPosted(mapperReq);
        BigDecimal currentUnPostedTotalAmount = sumList.stream().filter(p -> Objects.nonNull(p.getTotalAmount())).map(PartnerNotPostTransactionSumDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal currentOutstandingTotalAmount = sumList.stream().filter(p -> Objects.nonNull(p.getTotalAmount()) && "O".equals(p.getSumType())).map(PartnerNotPostTransactionSumDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer currentUnPostedTotalCount = sumList.stream().map(PartnerNotPostTransactionSumDTO::getTotalCount).reduce(0, Integer::sum);
        //计算下个账单日
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(organizationNumber);
        LocalDate nextProcessingDay = parmOrganizationInfo.getNextProcessingDay();
        int dayOfMonth = nextProcessingDay.getDayOfMonth();
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectVaildByMid(req.getAccountManagementId());
        //默认下个账单日在本月
        LocalDate nextStatementDate = LocalDate.of(nextProcessingDay.getYear(), nextProcessingDay.getMonthValue(), accountManagementInfo.getCycleDay());
        if (dayOfMonth > accountManagementInfo.getCycleDay()) {
            //如果下一处理日比账单日期大，则下个账单日在下个月
            nextStatementDate = nextStatementDate.plusMonths(1L);
        }

        PartnerNotPostTransactionResp result = new PartnerNotPostTransactionResp();
        result.setPage(page);
        result.setRows(rows);
        result.setTotal((long) totalUnderCondition);
        result.setTotalPage((long) (totalUnderCondition / rows + (totalUnderCondition % rows == 0 ? 0 : 1)));
        result.setStatementDate(nextStatementDate.format(DateTimeFormatter.ISO_DATE));
        result.setCurrentUnPostedTotalAmount(currentUnPostedTotalAmount);
        result.setCurrentUnPostedTotalCount(currentUnPostedTotalCount);
        result.setCurrentUnPostedCurrency(accountManagementInfo.getCurrency());
        result.setCurrentOutstandingTotalAmount(currentOutstandingTotalAmount);
        result.setTransObjList(resultList.stream().map(p -> {
            PostedTransactionTokenResDTO tokenResDTO = BeanMapping.copy(p, PostedTransactionTokenResDTO.class);
            //TODO 给第三方提供的交易类型，暂时用交易属性代替
            tokenResDTO.setPostingTransType(p.getTransactionAttribute());
            tokenResDTO.setTransactionDate(p.getTransactionDateTime().toLocalDate());
            return tokenResDTO;
        }).collect(Collectors.toList()));

        return result;
        */
        return null;
    }

    @Override
    public PageResultDTO<PostedTransactionTokenResDTO> findListPostTransToken(PostedTransaction req) {
        logger.debug("Query posted transactions by account statement id: {}", req.getAccountStatementId());
        String organizationNumber = OrgNumberUtils.getOrg();
        PostedTransaction mapperReq = new PostedTransaction();
        if (StringUtils.isAllBlank(req.getCardNumber(), req.getAccountManagementId())) {
            logger.error("Query posted transaction details, card number and account management number are both empty");
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_PARAMETER_IS_NULL);
        }
        if (StringUtils.isBlank(req.getAccountManagementId())) {
            //如果管理账户号为空，则通过卡号查询
            req.setAccountManagementId(findMidByCardNumber(req.getCardNumber()));
        }
        BeanUtils.copyProperties(req, mapperReq);
/* TODO 暂时注释

        if (Objects.nonNull(req.getTransactionDateStart())) {
            mapperReq.setTransactionDateStart(req.getTransactionDateStart().atTime(0, 0, 0, 0));
        }
        if (Objects.nonNull(req.getTransactionDateEnd())) {
            mapperReq.setTransactionDateEnd(req.getTransactionDateEnd().plusDays(1).atTime(0, 0, 0, 0));
        }
        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectTokenStatementDetail(mapperReq, req.getRows(), req.getRows() * (req.getPage() - 1));
        Map<String, ParmTransactionCode> transactionCodeMap = getTransactionCodeMap(organizationNumber);
        List<PostedTransactionResDTO> resultDtos = new ArrayList<>();
        for (PostedTransaction postedTransaction : postedTransactions) {
            PostedTransactionResDTO postedTransactionRes = new PostedTransactionResDTO();
            BeanUtils.copyProperties(postedTransaction, postedTransactionRes);
            ParmTransactionCode paramTransactionCode = transactionCodeMap.getOrDefault(postedTransaction.getPostingTransactionCode(), null);
            if (Objects.isNull(paramTransactionCode)) {
                logger.error("Failed to get transaction code parameters: organization number: {}, transaction code: {}", postedTransaction.getOrganizationNumber(), postedTransaction.getPostingTransactionCode());
                throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_PARM_TRANS_CODE_NOT_EXIST);
            }
            postedTransactionRes.setTransactionAttribute(paramTransactionCode.getTransactionAttribute());
            //年费交易特殊处理
            if (TransactionConstants.ANNUAL_FEE_TRXN_CODE.contains(paramTransactionCode.getTransactionCode())) {
                postedTransactionRes.setTransactionAttribute("N");
            }
            if (TransactionConstants.PHYSICAL_CARD_FEE_TRXN_CODE.contains(paramTransactionCode.getTransactionCode())) {
                postedTransactionRes.setTransactionAttribute("P");
            }
            if (TransactionConstants.REPLACE_CARD_FEE_TRXN_CODE.contains(paramTransactionCode.getTransactionCode())) {
                postedTransactionRes.setTransactionAttribute("R");
            }
            postedTransactionRes.setPostingTransactionCodeDesc(paramTransactionCode.getDescription());
            if ("PG005".equals(postedTransactionRes.getPostingTransactionCode())){
                postedTransactionRes.setMerchantName(null);
            }
            if (!"N".equals(postedTransactionRes.getIfiIndicator())){
                postedTransactionRes.setPostIndicator("1");
                resultDtos.add(postedTransactionRes);
            }
        }



        Integer page = req.getPage();
        Integer rows = req.getRows();

        Integer totalNumber = postedTransactionSelfMapper.selectTokenPostPageInfo(mapperReq);

        buildInstallTermInfo(resultDtos);

        return new PageResultDTO<>(page, rows, totalNumber, totalNumber / rows + (totalNumber % rows == 0 ? 0 : 1), resultDtos.stream().map(r -> {
            PostedTransactionTokenResDTO tokenResDTO = BeanMapping.copy(r, PostedTransactionTokenResDTO.class);
            //TODO 暂时用交易属性代替给第三方的交易类型
            tokenResDTO.setPostingTransType(r.getTransactionAttribute());
            tokenResDTO.setTransactionDateTime(r.getTransactionDate());
            tokenResDTO.setTransactionDate(r.getTransactionDate().toLocalDate());
            return tokenResDTO;
        }).collect(Collectors.toList()));

 */
        return null;
    }


    private String findMidByCardNumber(String cardNumber) {
        String orgNumber = OrgNumberUtils.getOrg();
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(cardNumber);
        if (cardAuthorizationInfo == null) {
            logger.error("Token transaction information query, card information is empty by card number: {}", cardNumber);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        List<ParmCardCurrencyInfo> parmCardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(orgNumber, cardAuthorizationInfo.getProductNumber());
        ParmCardCurrencyInfo parmCardCurrencyInfo = parmCardCurrencyInfoList.get(0);
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(orgNumber, cardAuthorizationInfo.getProductNumber());
        String accountCustomerId = LiabilityEnum.CORPORATE.getCode().equals(parmCardProductInfo.getLiability()) ? cardAuthorizationInfo.getCorporateCustomerId() : cardAuthorizationInfo.getPrimaryCustomerId();
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndCurr(accountCustomerId, parmCardProductInfo.getAccountProductNumber(), parmCardCurrencyInfo.getCurrencyCode());
        if (accountManagementInfo == null) {
            logger.error("Token transaction information query, account management information is empty by card number: {}, accountCustomerId: {}, accountProductNumber: {}, currency: {}", cardNumber, accountCustomerId, parmCardProductInfo.getAccountProductNumber(), parmCardCurrencyInfo.getCurrencyCode());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        return accountManagementInfo.getAccountManagementId();
    }
}
